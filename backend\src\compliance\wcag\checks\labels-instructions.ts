/**
 * WCAG-030: Labels or Instructions Check
 * Success Criterion: 3.3.2 Labels or Instructions (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import {
  FormAccessibilityAnalyzer,
  FormAccessibilityReport,
} from '../utils/form-accessibility-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export interface LabelsInstructionsConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class LabelsInstructionsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();

  async performCheck(config: LabelsInstructionsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LabelsInstructionsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-030',
      'Labels or Instructions',
      'understandable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeLabelsInstructionsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with form label analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-030',
        ruleName: 'Labels or Instructions',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'form-label-analysis',
          labelAssociationValidation: true,
          instructionDetection: true,
          ariaLabelValidation: true,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 40,
      },
    );
    const totalElements = enhancedEvidence.reduce(
      (sum, ev) => sum + ((ev as WcagEvidence & { elementCount?: number }).elementCount || 0),
      0,
    );
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'labels-instructions-analysis',
        confidence: 0.8,
        additionalData: {
          checkType: 'form-accessibility',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeLabelsInstructionsCheck(
    page: Page,
    _config: LabelsInstructionsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeValidation: true,
        analyzeGrouping: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze labels and instructions using enhanced analyzer
    const labelsAnalysis = await this.analyzeLabelsAndInstructionsEnhanced(
      page,
      formAccessibilityReport,
    );

    // Analyze instruction quality using AI semantic validation
    const instructionQualityAnalysis = await this.analyzeInstructionQuality(page);

    // Combine analysis results
    const allAnalyses = [labelsAnalysis, instructionQualityAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Enhanced labels and instructions analysis using FormAccessibilityAnalyzer
   */
  private async analyzeLabelsAndInstructionsEnhanced(
    _page: Page,
    formAccessibilityReport: FormAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form, formIndex: number) => {
      form.fields.forEach((field, fieldIndex: number) => {
        totalChecks++;

        // Check if field has proper labeling
        if (field.label.hasLabel && field.label.isAccessible) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Form field ${fieldIndex + 1} in form ${formIndex + 1} has proper labeling`,
            value: `${field.selector} - label: "${field.label.labelText}", accessible: ${field.label.isAccessible}`,
            selector: field.selector,
            severity: 'info',
          });
        } else {
          issues.push(
            `Form field ${fieldIndex + 1} in form ${formIndex + 1} lacks proper labeling`,
          );
          evidence.push({
            type: 'code',
            description: `Form field ${fieldIndex + 1} requires proper labeling`,
            value: `${field.selector} - hasLabel: ${field.label.hasLabel}, isAccessible: ${field.label.isAccessible}`,
            selector: field.selector,
            severity: 'error',
          });
          recommendations.push(
            `Add proper label to form field ${fieldIndex + 1} in form ${formIndex + 1}`,
          );
        }

        // Check for required field instructions
        if (field.validation.isRequired && !field.accessibility.hasDescription) {
          issues.push(`Required field ${fieldIndex + 1} lacks instructions`);
          evidence.push({
            type: 'code',
            description: `Required field ${fieldIndex + 1} needs instructions`,
            value: `${field.selector} - required field without instructions`,
            selector: field.selector,
            severity: 'warning',
          });
          recommendations.push(`Add instructions for required field ${fieldIndex + 1}`);
        }

        // Check for validation instructions
        if (field.validation.hasValidation && !field.validation.hasAccessibleErrors) {
          issues.push(`Field ${fieldIndex + 1} with validation lacks format instructions`);
          evidence.push({
            type: 'code',
            description: `Field ${fieldIndex + 1} needs validation instructions`,
            value: `${field.selector} - has validation but no format instructions`,
            selector: field.selector,
            severity: 'warning',
          });
          recommendations.push(`Add format instructions for field ${fieldIndex + 1}`);
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze instruction quality using AI semantic validation
   */
  private async analyzeInstructionQuality(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Find instruction elements
    const instructions = await page.$$eval(
      'label, .help-text, .instruction, [aria-describedby], .form-help, .field-description',
      (elements) => {
        return elements.map((element, index) => {
          const text = element.textContent?.trim() || '';
          const isVisible = (element as HTMLElement).offsetParent !== null;
          const associatedField =
            element.getAttribute('for') ||
            element.closest('form')?.querySelector(`[aria-describedby="${element.id}"]`);

          return {
            index,
            text,
            isVisible,
            hasAssociatedField: !!associatedField,
            selector: `instruction-${index}`,
            isEmpty: text.length === 0,
            isVague: text.length < 10 || (text.toLowerCase().includes('enter') && text.length < 20),
          };
        });
      },
    );

    const totalChecks = instructions.length;
    let passedChecks = 0;

    instructions.forEach((instruction, index) => {
      let instructionPassed = true;

      // Check if instruction is visible and has content
      if (!instruction.isVisible || instruction.isEmpty) {
        instructionPassed = false;
        issues.push(`Instruction ${index + 1} is not visible or empty`);
        evidence.push({
          type: 'code',
          description: `Instruction ${index + 1} visibility issue`,
          value: `visible: ${instruction.isVisible}, hasContent: ${!instruction.isEmpty}`,
          severity: 'error',
        });
        recommendations.push(
          `Ensure instruction ${index + 1} is visible and has meaningful content`,
        );
      }

      // Check for vague instructions
      if (instruction.isVague && instruction.text.length > 0) {
        issues.push(`Instruction ${index + 1} is too vague`);
        evidence.push({
          type: 'code',
          description: `Instruction ${index + 1} needs more specific content`,
          value: `instruction: "${instruction.text}"`,
          severity: 'warning',
        });
        recommendations.push(`Make instruction ${index + 1} more specific and helpful`);
      }

      if (instructionPassed) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Instruction ${index + 1} is properly implemented`,
          value: `"${instruction.text}" - visible and meaningful`,
          severity: 'info',
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }
}
