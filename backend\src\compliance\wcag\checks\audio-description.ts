/**
 * WCAG-034: Audio Description Check (Enhanced)
 * Success Criterion: 1.2.3 Audio Description or Media Alternative (Prerecorded) (Level A)
 * Enhanced with MultimediaAccessibilityTester integration
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import MultimediaAccessibilityTester, {
  MultimediaAccessibilityReport,
} from '../utils/multimedia-accessibility-tester';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';

export interface AudioDescriptionConfig extends EnhancedCheckConfig {
  enableMultimediaAccessibilityTesting?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAdvancedMediaAnalysis?: boolean;
}

export class AudioDescriptionCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private multimediaAccessibilityTester = MultimediaAccessibilityTester.getInstance();
  private contentQualityAnalyzer = new ContentQualityAnalyzer();

  // Keywords that indicate decorative content
  private readonly decorativeIndicators = [
    'background video',
    'decorative',
    'ambient',
    'loop',
    'autoplay',
    'banner video',
    'hero video',
    'mood video',
  ];

  async performCheck(config: AudioDescriptionConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: AudioDescriptionConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 4000,
      },
      enableMultimediaAccessibilityTesting: true,
      enableContentQualityAnalysis: true,
      enableAdvancedMediaAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-034',
      'Audio Description',
      'perceivable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeAudioDescriptionCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with audio description analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-034',
        ruleName: 'Audio Description',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'audio-description-analysis',
          multimediaAccessibilityTesting: enhancedConfig.enableMultimediaAccessibilityTesting,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
          advancedMediaAnalysis: enhancedConfig.enableAdvancedMediaAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 25,
      },
    );

    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'audio-description-analysis',
        confidence: 0.8,
        additionalData: {
          checkType: 'media-accessibility',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeAudioDescriptionCheck(
    page: Page,
    _config: AudioDescriptionConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced multimedia accessibility testing
    const multimediaReport =
      await this.multimediaAccessibilityTester.testMultimediaAccessibility(page);

    // Analyze video elements for audio description requirements using MultimediaAccessibilityTester
    const audioDescriptionAnalysis = await this.analyzeAudioDescriptionRequirements(
      page,
      multimediaReport,
    );

    // Combine analysis results
    const totalChecks = audioDescriptionAnalysis.totalChecks;
    const passedChecks = audioDescriptionAnalysis.passedChecks;

    evidence.push(...audioDescriptionAnalysis.evidence);
    issues.push(...audioDescriptionAnalysis.issues);
    recommendations.push(...audioDescriptionAnalysis.recommendations);

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze audio description requirements using MultimediaAccessibilityTester
   */
  private async analyzeAudioDescriptionRequirements(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const videoElements = multimediaReport.videoElements;
    const totalChecks = videoElements.length;
    let passedChecks = 0;

    videoElements.forEach((video, index) => {
      // Check if video needs audio description (has visual content)
      const needsAudioDescription = this.videoNeedsAudioDescription(video);

      if (!needsAudioDescription) {
        // Video doesn't need audio description (e.g., audio-only or decorative)
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Video ${index + 1} does not require audio description`,
          value: `${video.selector} - no visual content requiring description`,
          selector: video.selector,
          severity: 'info',
        });
      } else {
        // Video needs audio description - check if it has it
        if (
          video.audioDescription.hasAudioDescription ||
          video.transcript.hasTranscript ||
          video.captions.hasTextTracks
        ) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Video ${index + 1} has audio description or alternative`,
            value: `${video.selector} - audioDescription: ${video.audioDescription.hasAudioDescription}, transcript: ${video.transcript.hasTranscript}`,
            selector: video.selector,
            severity: 'info',
          });
        } else {
          issues.push(`Video ${index + 1} lacks audio description or media alternative`);
          evidence.push({
            type: 'code',
            description: `Video ${index + 1} requires audio description or media alternative`,
            value: `${video.selector} - no audio description or alternative found`,
            selector: video.selector,
            severity: 'error',
          });
          recommendations.push(`Add audio description or media alternative for video ${index + 1}`);
        }
      }

      // Add issues from MultimediaAccessibilityTester
      if (video.issues.length > 0) {
        video.issues.forEach((issue) => {
          issues.push(`Video ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (video.recommendations.length > 0) {
        video.recommendations.forEach((recommendation) => {
          recommendations.push(`Video ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Determine if a video needs audio description based on content analysis
   */
  private videoNeedsAudioDescription(video: {
    selector: string;
    audioDescription: { hasAudioDescription: boolean };
  }): boolean {
    // Videos with audio description tracks don't need additional description
    if (video.audioDescription.hasAudioDescription) {
      return false;
    }

    // Check if video appears to be decorative or audio-only
    const selector = video.selector.toLowerCase();
    const isDecorative = this.decorativeIndicators.some((indicator) =>
      selector.includes(indicator),
    );

    if (isDecorative) {
      return false;
    }

    // If video has visual content and no audio description, it likely needs it
    return true;
  }
}
