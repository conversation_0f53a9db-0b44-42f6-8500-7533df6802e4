/**
 * WCAG-055: Label in Name Check
 * Success Criterion: 2.5.3 Label in Name (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';

export interface LabelInNameConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableFormAccessibilityAnalysis?: boolean;
}

export class LabelInNameCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();

  async performCheck(config: LabelInNameConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LabelInNameConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableFormAccessibilityAnalysis: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-055',
      'Label in Name',
      'operable',
      0.0458,
      'A',
      enhancedConfig,
      this.executeLabelInNameCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with label-name consistency analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-055',
        ruleName: 'Label in Name',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'label-name-consistency-analysis',
          accessibleNameValidation: true,
          visualLabelComparison: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 40,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeLabelInNameCheck(
    page: Page,
    _config: LabelInNameConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Enhanced form accessibility analysis using FormAccessibilityAnalyzer
    const formAccessibilityReport = await this.formAccessibilityAnalyzer.analyzeFormAccessibility(
      page,
      {
        analyzeLabels: true,
        analyzeKeyboardAccess: true,
        strictMode: true,
      },
    );

    // Analyze accessible name validation using enhanced analyzer
    const accessibleNameAnalysis = await this.analyzeAccessibleNameValidation(
      page,
      formAccessibilityReport,
    );

    // Analyze label-name consistency
    const labelNameConsistencyAnalysis = await this.analyzeLabelNameConsistency(page);

    // Analyze speech recognition compatibility
    const speechRecognitionAnalysis = await this.analyzeSpeechRecognitionCompatibility(page);

    // Combine analysis results
    const allAnalyses = [accessibleNameAnalysis, labelNameConsistencyAnalysis, speechRecognitionAnalysis];
    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze accessible name validation using FormAccessibilityAnalyzer
   */
  private async analyzeAccessibleNameValidation(
    page: Page,
    formAccessibilityReport: {
      forms: Array<{
        fields: Array<{
          selector: string;
          label: {
            hasLabel: boolean;
            labelText?: string;
            isAccessible: boolean;
          };
        }>;
      }>;
    },
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    let totalChecks = 0;
    let passedChecks = 0;

    formAccessibilityReport.forms.forEach((form, formIndex: number) => {
      form.fields.forEach((field, fieldIndex: number) => {
        totalChecks++;

        // Check if field has proper accessible name that includes visible label
        if (field.label.hasLabel && field.label.labelText) {
          // For this check, we'll use the label text as both visible and accessible name
          // since the FormAccessibilityAnalyzer doesn't provide separate accessible name
          const labelIncludedInName = this.checkLabelInAccessibleName(
            field.label.labelText,
            field.label.labelText,
          );

          if (labelIncludedInName) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Field ${fieldIndex + 1} in form ${formIndex + 1} has proper label`,
              value: `${field.selector} - label: "${field.label.labelText}"`,
              selector: field.selector,
              severity: 'info',
            });
          } else {
            issues.push(`Field ${fieldIndex + 1} label validation failed`);
            evidence.push({
              type: 'code',
              description: `Field ${fieldIndex + 1} needs proper label validation`,
              value: `${field.selector} - label: "${field.label.labelText}"`,
              selector: field.selector,
              severity: 'error',
            });
            recommendations.push(`Ensure field ${fieldIndex + 1} has proper label validation`);
          }
        } else {
          issues.push(`Field ${fieldIndex + 1} lacks proper labeling for name validation`);
          evidence.push({
            type: 'code',
            description: `Field ${fieldIndex + 1} needs proper label and accessible name`,
            value: `${field.selector} - hasLabel: ${field.label.hasLabel}`,
            selector: field.selector,
            severity: 'error',
          });
          recommendations.push(`Add proper label and accessible name to field ${fieldIndex + 1}`);
        }
      });
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze label-name consistency for interactive elements
   */
  private async analyzeLabelNameConsistency(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check interactive elements with visible text
    const interactiveElements = await page.$$eval(
      'button, [role="button"], a, [role="link"], input[type="submit"], input[type="button"]',
      (elements) => {
        return elements.map((element, index) => {
          const visibleText = element.textContent?.trim() || element.getAttribute('value') || '';
          const accessibleName =
            element.getAttribute('aria-label') ||
            element.getAttribute('aria-labelledby') ||
            element.getAttribute('title') ||
            visibleText;

          return {
            index,
            selector: `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
            tagName: element.tagName.toLowerCase(),
            visibleText,
            accessibleName,
            hasVisibleText: visibleText.length > 0,
            hasAccessibleName: accessibleName.length > 0,
          };
        });
      },
    );

    const totalChecks = interactiveElements.filter((el) => el.hasVisibleText).length;
    let passedChecks = 0;

    interactiveElements.forEach((element, index) => {
      if (element.hasVisibleText) {
        const labelIncluded = this.checkLabelInAccessibleName(
          element.visibleText,
          element.accessibleName,
        );

        if (labelIncluded) {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Interactive element ${index + 1} has consistent label and accessible name`,
            value: `${element.selector} - visible: "${element.visibleText}", accessible: "${element.accessibleName}"`,
            selector: element.selector,
            severity: 'info',
          });
        } else {
          issues.push(
            `Interactive element ${index + 1} has inconsistent label and accessible name`,
          );
          evidence.push({
            type: 'code',
            description: `Interactive element ${index + 1} needs consistent naming`,
            value: `${element.selector} - visible: "${element.visibleText}", accessible: "${element.accessibleName}"`,
            selector: element.selector,
            severity: 'error',
          });
          recommendations.push(
            `Ensure accessible name for element ${index + 1} includes the visible text`,
          );
        }
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze speech recognition compatibility
   */
  private async analyzeSpeechRecognitionCompatibility(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check for elements that might cause speech recognition issues
    const speechAnalysis = await page.$$eval('*[aria-label], *[aria-labelledby]', (elements) => {
      return elements.map((element, index) => {
        const visibleText = element.textContent?.trim() || '';
        const ariaLabel = element.getAttribute('aria-label') || '';
        const ariaLabelledby = element.getAttribute('aria-labelledby') || '';

        // Check for common speech recognition issues
        const hasSymbolsInLabel = /[^\w\s]/.test(ariaLabel);
        const hasNumbersAsWords = /\b(one|two|three|four|five|six|seven|eight|nine|ten)\b/i.test(
          ariaLabel,
        );
        const hasAbbreviations = /\b[A-Z]{2,}\b/.test(ariaLabel);

        return {
          index,
          selector: `element-${index}`,
          visibleText,
          ariaLabel,
          ariaLabelledby,
          hasSymbolsInLabel,
          hasNumbersAsWords,
          hasAbbreviations,
          hasSpeechIssues: hasSymbolsInLabel || hasNumbersAsWords || hasAbbreviations,
        };
      });
    });

    const totalChecks = speechAnalysis.length;
    let passedChecks = 0;

    speechAnalysis.forEach((element, index) => {
      if (!element.hasSpeechIssues) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Element ${index + 1} is compatible with speech recognition`,
          value: `aria-label: "${element.ariaLabel}" - no speech recognition issues`,
          severity: 'info',
        });
      } else {
        issues.push(`Element ${index + 1} may have speech recognition compatibility issues`);
        evidence.push({
          type: 'code',
          description: `Element ${index + 1} needs speech recognition optimization`,
          value: `aria-label: "${element.ariaLabel}" - symbols: ${element.hasSymbolsInLabel}, numbers: ${element.hasNumbersAsWords}, abbreviations: ${element.hasAbbreviations}`,
          severity: 'warning',
        });
        recommendations.push(
          `Optimize element ${index + 1} for speech recognition by avoiding symbols, spelling out numbers, and expanding abbreviations`,
        );
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Check if visible label text is included in accessible name
   */
  private checkLabelInAccessibleName(labelText: string, accessibleName: string): boolean {
    if (!labelText || !accessibleName) return false;

    const normalizedLabel = this.normalizeText(labelText);
    const normalizedAccessible = this.normalizeText(accessibleName);

    // Check if label is exactly the accessible name
    if (normalizedLabel === normalizedAccessible) return true;

    // Check if label is contained in accessible name
    if (normalizedAccessible.includes(normalizedLabel)) return true;

    // Check if words from label appear in accessible name
    const labelWords = normalizedLabel.split(/\s+/);
    const accessibleWords = normalizedAccessible.split(/\s+/);

    return labelWords.every((word) => accessibleWords.includes(word));
  }

  /**
   * Normalize text for comparison
   */
  private normalizeText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, '') // Remove punctuation
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }
}
