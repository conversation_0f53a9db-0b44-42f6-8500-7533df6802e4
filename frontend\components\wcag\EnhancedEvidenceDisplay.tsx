/**
 * Enhanced Evidence Display Component
 * Displays WCAG evidence with enhanced features like fix examples and element counts
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ChevronDown,
  ChevronRight,
  Code,
  ExternalLink,
  Info,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Clock,
  Target,
  Zap,
} from 'lucide-react';
import {
  WcagEvidenceEnhanced,
  WcagFixExample,
  WcagElementCounts,
  WcagPerformanceMetrics,
  EnhancedEvidenceDisplayProps,
} from '@/types/wcag';

const EvidenceIcon = ({ type, severity }: { type: string; severity?: string }) => {
  if (severity === 'error') return <XCircle className="h-4 w-4 text-red-500" />;
  if (severity === 'warning') return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
  if (severity === 'info') return <Info className="h-4 w-4 text-blue-500" />;
  if (type === 'code') return <Code className="h-4 w-4 text-gray-500" />;
  return <CheckCircle className="h-4 w-4 text-green-500" />;
};

const SeverityBadge = ({ severity }: { severity?: string }) => {
  const variants = {
    error: 'destructive',
    warning: 'secondary',
    info: 'outline',
    critical: 'destructive',
  } as const;

  return (
    <Badge variant={variants[severity as keyof typeof variants] || 'outline'}>
      {severity || 'info'}
    </Badge>
  );
};

const FixExampleDisplay = ({ fixExample }: { fixExample: WcagFixExample }) => {
  const [activeTab, setActiveTab] = useState('before');

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle className="text-sm flex items-center gap-2">
          <Zap className="h-4 w-4" />
          Fix Example
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-600 mb-4">{fixExample.description}</p>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="before">Before</TabsTrigger>
            <TabsTrigger value="after">After</TabsTrigger>
          </TabsList>

          <TabsContent value="before" className="mt-4">
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <code className="text-sm text-red-800">{fixExample.before}</code>
            </div>
          </TabsContent>

          <TabsContent value="after" className="mt-4">
            <div className="bg-green-50 border border-green-200 rounded-md p-3">
              <code className="text-sm text-green-800">{fixExample.after}</code>
            </div>
          </TabsContent>
        </Tabs>

        {fixExample.codeExample && (
          <Collapsible className="mt-4">
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm" className="w-full">
                <Code className="h-4 w-4 mr-2" />
                View Complete Example
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2">
              <pre className="bg-gray-100 p-3 rounded-md text-xs overflow-x-auto">
                <code>{fixExample.codeExample}</code>
              </pre>
            </CollapsibleContent>
          </Collapsible>
        )}

        {fixExample.resources && fixExample.resources.length > 0 && (
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Resources:</h4>
            <ul className="space-y-1">
              {fixExample.resources.map((resource, index) => (
                <li key={index}>
                  <a
                    href={resource}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
                  >
                    <ExternalLink className="h-3 w-3" />
                    {resource}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const ElementCountsDisplay = ({ elementCounts }: { elementCounts: WcagElementCounts }) => {
  const successRate =
    elementCounts.total > 0 ? Math.round((elementCounts.passed / elementCounts.total) * 100) : 0;

  return (
    <div className="flex items-center gap-4 text-sm">
      <div className="flex items-center gap-1">
        <Target className="h-4 w-4 text-gray-500" />
        <span className="font-medium">Elements:</span>
      </div>
      <div className="flex gap-3">
        <span className="text-green-600">{elementCounts.passed} passed</span>
        <span className="text-red-600">{elementCounts.failed} failed</span>
        <span className="text-gray-600">{elementCounts.total} total</span>
        <Badge variant={successRate >= 80 ? 'default' : 'secondary'}>{successRate}% success</Badge>
      </div>
    </div>
  );
};

const PerformanceMetricsDisplay = ({ performance }: { performance: WcagPerformanceMetrics }) => {
  return (
    <div className="flex items-center gap-4 text-sm text-gray-600">
      <div className="flex items-center gap-1">
        <Clock className="h-4 w-4" />
        <span>{performance.scanDuration}ms</span>
      </div>
      <div className="flex items-center gap-1">
        <Target className="h-4 w-4" />
        <span>{performance.elementsAnalyzed} analyzed</span>
      </div>
      {performance.cacheHitRate !== undefined && (
        <div className="flex items-center gap-1">
          <Zap className="h-4 w-4" />
          <span>{Math.round(performance.cacheHitRate * 100)}% cached</span>
        </div>
      )}
    </div>
  );
};

const EvidenceItem = ({
  evidence,
  showFixExamples = true,
  showElementCounts = true,
  showPerformanceMetrics = false,
}: {
  evidence: WcagEvidenceEnhanced;
  showFixExamples?: boolean;
  showElementCounts?: boolean;
  showPerformanceMetrics?: boolean;
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3">
            <EvidenceIcon type={evidence.type} severity={evidence.severity} />
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-medium text-sm">{evidence.description}</h4>
                <SeverityBadge severity={evidence.severity} />
              </div>
              {evidence.selector && (
                <code className="text-xs bg-gray-100 px-2 py-1 rounded">{evidence.selector}</code>
              )}
            </div>
          </div>
          {(evidence.fixExample || evidence.elementCount || evidence.metadata) && (
            <Button variant="ghost" size="sm" onClick={() => setIsExpanded(!isExpanded)}>
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <div className="bg-gray-50 p-3 rounded-md mb-3">
          <code className="text-sm">{evidence.value}</code>
        </div>

        {showElementCounts && evidence.elementCount !== undefined && (
          <div className="mb-3">
            <ElementCountsDisplay
              elementCounts={{
                total: evidence.elementCount,
                failed: evidence.severity === 'error' ? evidence.elementCount : 0,
                passed: evidence.severity === 'error' ? 0 : evidence.elementCount,
              }}
            />
          </div>
        )}

        {showPerformanceMetrics && evidence.metadata && (
          <div className="mb-3">
            <PerformanceMetricsDisplay
              performance={{
                scanDuration: evidence.metadata.scanDuration || 0,
                elementsAnalyzed: evidence.metadata.elementsAnalyzed || 0,
              }}
            />
          </div>
        )}

        {isExpanded && (
          <div className="space-y-4">
            {showFixExamples && evidence.fixExample && (
              <FixExampleDisplay fixExample={evidence.fixExample} />
            )}

            {evidence.metadata?.checkSpecificData && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <details>
                    <summary className="cursor-pointer font-medium">Additional Details</summary>
                    <pre className="mt-2 text-xs">
                      {JSON.stringify(evidence.metadata.checkSpecificData, null, 2)}
                    </pre>
                  </details>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export const EnhancedEvidenceDisplay: React.FC<EnhancedEvidenceDisplayProps> = ({
  evidence,
  showFixExamples = true,
  showElementCounts = true,
  showPerformanceMetrics = false,
}) => {
  if (!evidence || evidence.length === 0) {
    return (
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>No evidence available for this check.</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      {evidence.map((item, index) => (
        <EvidenceItem
          key={index}
          evidence={item}
          showFixExamples={showFixExamples}
          showElementCounts={showElementCounts}
          showPerformanceMetrics={showPerformanceMetrics}
        />
      ))}
    </div>
  );
};

export default EnhancedEvidenceDisplay;
