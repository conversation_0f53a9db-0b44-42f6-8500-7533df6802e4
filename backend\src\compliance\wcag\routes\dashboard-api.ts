/**
 * Dashboard API Routes
 * Provides REST API endpoints for the WCAG real-time monitoring dashboard
 */

import { Router, Request, Response } from 'express';
import { RealTimeMonitoringDashboard } from '../utils/real-time-monitoring-dashboard';
import DashboardWebSocketService from '../utils/dashboard-websocket-service';
import { PerformanceIntegrationBridge } from '../utils/performance-integration-bridge';
import logger from '../../../utils/logger';

const router = Router();

// Get dashboard instances
const dashboard = RealTimeMonitoringDashboard.getInstance();
const webSocketService = DashboardWebSocketService.getInstance();
const performanceBridge = PerformanceIntegrationBridge.getInstance();

/**
 * GET /api/wcag/dashboard/metrics
 * Get current dashboard metrics
 */
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const currentMetrics = dashboard.getCurrentMetrics();

    if (!currentMetrics) {
      return res.status(404).json({
        success: false,
        message: 'No metrics available',
        data: null,
      });
    }

    res.json({
      success: true,
      message: 'Current dashboard metrics retrieved',
      data: currentMetrics,
    });
  } catch (error) {
    logger.error('Failed to get dashboard metrics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard metrics',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/wcag/dashboard/metrics/history
 * Get dashboard metrics history
 */
router.get('/metrics/history', async (req: Request, res: Response) => {
  try {
    const hours = parseInt(req.query.hours as string) || 24;
    const metricsHistory = dashboard.getMetricsHistory(hours);

    res.json({
      success: true,
      message: `Dashboard metrics history for last ${hours} hours retrieved`,
      data: {
        hours,
        metrics: metricsHistory,
        count: metricsHistory.length,
      },
    });
  } catch (error) {
    logger.error('Failed to get dashboard metrics history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard metrics history',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/wcag/dashboard/config
 * Get dashboard configuration
 */
router.get('/config', async (req: Request, res: Response) => {
  try {
    const config = dashboard.getConfig();

    res.json({
      success: true,
      message: 'Dashboard configuration retrieved',
      data: config,
    });
  } catch (error) {
    logger.error('Failed to get dashboard config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve dashboard configuration',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * PUT /api/wcag/dashboard/config
 * Update dashboard configuration
 */
router.put('/config', async (req: Request, res: Response) => {
  try {
    const newConfig = req.body;

    // Validate configuration
    if (typeof newConfig !== 'object' || newConfig === null) {
      return res.status(400).json({
        success: false,
        message: 'Invalid configuration format',
      });
    }

    dashboard.updateConfig(newConfig);
    const updatedConfig = dashboard.getConfig();

    res.json({
      success: true,
      message: 'Dashboard configuration updated',
      data: updatedConfig,
    });
  } catch (error) {
    logger.error('Failed to update dashboard config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update dashboard configuration',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /api/wcag/dashboard/start
 * Start dashboard monitoring
 */
router.post('/start', async (req: Request, res: Response) => {
  try {
    dashboard.start();

    res.json({
      success: true,
      message: 'Dashboard monitoring started',
      data: {
        isRunning: true,
        config: dashboard.getConfig(),
      },
    });
  } catch (error) {
    logger.error('Failed to start dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start dashboard monitoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /api/wcag/dashboard/stop
 * Stop dashboard monitoring
 */
router.post('/stop', async (req: Request, res: Response) => {
  try {
    dashboard.stop();

    res.json({
      success: true,
      message: 'Dashboard monitoring stopped',
      data: {
        isRunning: false,
      },
    });
  } catch (error) {
    logger.error('Failed to stop dashboard:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop dashboard monitoring',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/wcag/dashboard/websocket/status
 * Get WebSocket service status
 */
router.get('/websocket/status', async (req: Request, res: Response) => {
  try {
    const connectedClients = webSocketService.getConnectedClientsCount();
    const subscriptions = webSocketService.getSubscriptionsSummary();

    res.json({
      success: true,
      message: 'WebSocket service status retrieved',
      data: {
        connectedClients,
        subscriptions,
        isRunning: connectedClients > 0, // Simplified check
      },
    });
  } catch (error) {
    logger.error('Failed to get WebSocket status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve WebSocket service status',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /api/wcag/dashboard/websocket/start
 * Start WebSocket service
 */
router.post('/websocket/start', async (req: Request, res: Response) => {
  try {
    const port = parseInt(req.body.port) || 8081;
    webSocketService.start(port);

    res.json({
      success: true,
      message: `WebSocket service started on port ${port}`,
      data: {
        port,
        isRunning: true,
      },
    });
  } catch (error) {
    logger.error('Failed to start WebSocket service:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start WebSocket service',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /api/wcag/dashboard/websocket/stop
 * Stop WebSocket service
 */
router.post('/websocket/stop', async (req: Request, res: Response) => {
  try {
    webSocketService.stop();

    res.json({
      success: true,
      message: 'WebSocket service stopped',
      data: {
        isRunning: false,
      },
    });
  } catch (error) {
    logger.error('Failed to stop WebSocket service:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to stop WebSocket service',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * GET /api/wcag/dashboard/performance/integrated-report/:scanId
 * Get integrated performance report for a specific scan
 */
router.get('/performance/integrated-report/:scanId', async (req: Request, res: Response) => {
  try {
    const { scanId } = req.params;

    if (!scanId) {
      return res.status(400).json({
        success: false,
        message: 'Scan ID is required',
      });
    }

    const integratedReport = performanceBridge.generateIntegratedReport(scanId);

    if (!integratedReport) {
      return res.status(404).json({
        success: false,
        message: 'No integrated performance report found for this scan',
        data: null,
      });
    }

    res.json({
      success: true,
      message: 'Integrated performance report retrieved',
      data: integratedReport,
    });
  } catch (error) {
    logger.error('Failed to get integrated performance report:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve integrated performance report',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

/**
 * POST /api/wcag/dashboard/alert
 * Send custom alert to dashboard
 */
router.post('/alert', async (req: Request, res: Response) => {
  try {
    const { type, severity, message, scanId, ruleId } = req.body;

    if (!type || !severity || !message) {
      return res.status(400).json({
        success: false,
        message: 'Alert type, severity, and message are required',
      });
    }

    if (!['low', 'medium', 'high', 'critical'].includes(severity)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid severity level',
      });
    }

    webSocketService.sendAlert({
      type,
      severity,
      message,
      scanId,
      ruleId,
    });

    res.json({
      success: true,
      message: 'Alert sent to dashboard',
      data: {
        type,
        severity,
        message,
        scanId,
        ruleId,
      },
    });
  } catch (error) {
    logger.error('Failed to send dashboard alert:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send dashboard alert',
      error: error instanceof Error ? error.message : 'Unknown error',
    });
  }
});

export default router;
