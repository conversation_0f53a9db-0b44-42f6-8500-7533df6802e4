{"name": "backend", "version": "0.1.0", "private": true, "main": "dist/src/index.js", "scripts": {"dev": "cross-env DEBUG=keycloak* nodemon --exec ts-node -r tsconfig-paths/register src/index.ts", "build": "tsc -p .", "start": "cross-env TS_NODE_BASEURL=./dist node -r tsconfig-paths/register dist/src/index.js", "lint": "eslint . --ext .ts --no-cache", "lint:fix": "eslint . --ext .ts --fix --no-cache", "test": "cross-env NODE_ENV=test jest", "test:hipaa-security": "cross-env NODE_ENV=test ts-node -r tsconfig-paths/register src/compliance/hipaa/security/__tests__/run-tests.ts", "test:hipaa-security:unit": "cross-env NODE_ENV=test jest --testPathPattern=\"src/compliance/hipaa/security/__tests__/(services|tests)/.*\\.test\\.ts$\"", "test:hipaa-security:integration": "cross-env NODE_ENV=test jest --testPathPattern=\"src/compliance/hipaa/security/__tests__/integration/.*\\.test\\.ts$\"", "test:hipaa-security:coverage": "cross-env NODE_ENV=test jest --testPathPattern=\"src/compliance/hipaa/security/__tests__\" --coverage --coverageDirectory=coverage/hipaa-security", "test:wcag-integration": "cross-env NODE_ENV=test ts-node -r tsconfig-paths/register src/compliance/wcag/testing/run-integration-tests.ts", "test:wcag-simple": "cross-env NODE_ENV=test ts-node -r tsconfig-paths/register src/compliance/wcag/testing/simple-integration-test.ts", "test:wcag-basic": "cross-env NODE_ENV=test ts-node -r tsconfig-paths/register src/compliance/wcag/testing/basic-validation-test.ts", "test:wcag-enhanced": "cross-env NODE_ENV=test ts-node -r tsconfig-paths/register src/compliance/wcag/testing/enhanced-checks-validation.ts", "knex": "cross-env TS_NODE_PROJECT=./tsconfig.json npx --node-options=\"-r ./preload-env.js -r ts-node/register -r tsconfig-paths/register\" knex --knexfile knexfile.ts", "migrate:latest": "npm run knex migrate:latest", "migrate:rollback": "npm run knex migrate:rollback", "migrate:make": "npm run knex migrate:make -- -x ts", "seed:run": "npm run knex seed:run"}, "dependencies": {"@types/express-rate-limit": "^5.1.3", "@types/pdfkit": "^0.14.0", "@xenova/transformers": "^2.17.2", "axe-core": "^4.10.2", "axios": "^1.10.0", "cheerio": "^1.1.0", "color": "^4.2.3", "colorjs.io": "^0.5.2", "compromise": "^14.14.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "flesch-kincaid": "^2.0.1", "get-contrast": "^3.0.0", "helmet": "^7.1.0", "jsdom": "^24.0.0", "keycloak-connect": "^26.1.1", "knex": "^3.1.0", "natural": "^8.1.0", "node-fetch": "^2.7.0", "node-html-parser": "^7.0.1", "pdfkit": "^0.17.1", "pg": "^8.16.0", "puppeteer": "^24.10.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "readability-js": "^1.0.7", "sentiment": "^5.0.2", "ssl-checker": "^2.0.10", "syllable": "^5.0.1", "tsconfig-paths": "^3.15.0", "uuid": "^11.1.0", "validator": "^13.15.15", "zod": "^3.22.4"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@types/color": "^4.2.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-session": "^1.18.1", "@types/express-validator": "^2.20.33", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.6", "@types/keycloak-connect": "^4.5.4", "@types/node": "^20.11.20", "@types/pg": "^8.11.0", "@types/puppeteer": "^5.4.7", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/validator": "^13.15.2", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "babel-jest": "^29.7.0", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.5.1", "jest": "^29.7.0", "nodemon": "^3.0.3", "prettier": "^3.6.2", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}