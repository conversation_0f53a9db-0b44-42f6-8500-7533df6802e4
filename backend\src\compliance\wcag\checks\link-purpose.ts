/**
 * WCAG-026: Link Purpose Check
 * Success Criterion: 2.4.4 Link Purpose (In Context) (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';



export interface LinkPurposeConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableContextAnalysis?: boolean;
  enableLinkQualityAssessment?: boolean;
}

export class LinkPurposeCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();

  // Generic/non-descriptive link text patterns
  private readonly genericPatterns = [
    /^click\s+here$/i,
    /^here$/i,
    /^more$/i,
    /^read\s+more$/i,
    /^learn\s+more$/i,
    /^continue$/i,
    /^next$/i,
    /^previous$/i,
    /^back$/i,
    /^link$/i,
    /^this\s+link$/i,
    /^download$/i,
    /^view$/i,
    /^see\s+more$/i,
    /^details$/i,
    /^info$/i,
    /^go$/i,
    /^submit$/i,
    /^button$/i,
  ];

  async performCheck(config: LinkPurposeConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LinkPurposeConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-026',
      'Link Purpose',
      'operable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeLinkPurposeCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with link purpose analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-026',
        ruleName: 'Link Purpose',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'link-purpose-analysis',
          linkAnalysis: true,
          contextAnalysis: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 40,
      },
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter((ev) => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'link-text-analysis',
        confidence: 0.9,
        additionalData: {
          checkType: 'content-analysis',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeLinkPurposeCheck(
    page: Page,
    _config: LinkPurposeConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze all links on the page
    const linkAnalysis = await page.evaluate(() => {
      const links = Array.from(document.querySelectorAll('a[href]'));

      return links.map((link, index) => {
        const href = link.getAttribute('href') || '';
        const text = link.textContent?.trim() || '';
        const ariaLabel = link.getAttribute('aria-label');
        const title = link.getAttribute('title');

        // Calculate accessible name (aria-label > text content > title)
        let accessibleName = '';
        if (ariaLabel) {
          accessibleName = ariaLabel.trim();
        } else if (text) {
          accessibleName = text;
        } else if (title) {
          accessibleName = title.trim();
        }

        // Get surrounding context for better analysis
        const parent = link.parentElement;
        const context = parent?.textContent?.trim().substring(0, 100) || '';

        return {
          href,
          text,
          ariaLabel,
          title,
          accessibleName,
          selector: `a:nth-of-type(${index + 1})`,
          isDescriptive: false, // Will be calculated
          isGeneric: false, // Will be calculated
          isEmpty: !accessibleName,
          context,
        };
      });
    });

    const scanDuration = Date.now() - startTime;
    const totalLinks = linkAnalysis.length;
    let problematicLinks = 0;
    let score = 100;

    if (totalLinks === 0) {
      // No links found - perfect score but add info
      evidence.push({
        type: 'info',
        description: 'No links found on page',
        value: 'Page contains no anchor elements with href attributes',
        selector: 'a[href]',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalLinks: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Consider adding navigation links if appropriate for the page'],
      };
    }

    // Analyze each link
    linkAnalysis.forEach((link, index) => {
      const isGeneric = this.isGenericLinkText(link.accessibleName);
      const isEmpty = !link.accessibleName;
      const isDescriptive = !isGeneric && !isEmpty && link.accessibleName.length >= 3;

      if (isEmpty) {
        problematicLinks++;
        issues.push(`Link ${index + 1}: Empty link text`);

        evidence.push({
          type: 'error',
          description: 'Link with empty accessible name',
          value: `<a href="${link.href}"></a>`,
          selector: link.selector,
          elementCount: 1,
          affectedSelectors: [link.selector],
          severity: 'error',
          fixExample: {
            before: `<a href="${link.href}"></a>`,
            after: `<a href="${link.href}">Descriptive link text</a>`,
            description: 'Add descriptive text that explains the link purpose',
            codeExample: `
<!-- Before -->
<a href="/contact"></a>

<!-- After -->
<a href="/contact">Contact us</a>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/link-purpose-in-context.html',
              'https://webaim.org/techniques/hypertext/link_text',
            ],
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              linkHref: link.href,
              linkText: link.text,
              ariaLabel: link.ariaLabel || '',
              title: link.title || '',
              isEmpty: true,
            },
          },
        });
      } else if (isGeneric) {
        problematicLinks++;
        issues.push(`Link ${index + 1}: Generic link text "${link.accessibleName}"`);

        evidence.push({
          type: 'error',
          description: 'Link with generic/non-descriptive text',
          value: `<a href="${link.href}">${link.accessibleName}</a>`,
          selector: link.selector,
          elementCount: 1,
          affectedSelectors: [link.selector],
          severity: 'error',
          fixExample: {
            before: `<a href="${link.href}">${link.accessibleName}</a>`,
            after: `<a href="${link.href}">Descriptive purpose text</a>`,
            description: 'Replace generic text with specific description of link purpose',
            codeExample: `
<!-- Before -->
<a href="/services">Click here</a>
<a href="/about">More</a>
<a href="/contact">Read more</a>

<!-- After -->
<a href="/services">View our services</a>
<a href="/about">About our company</a>
<a href="/contact">Contact us</a>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/link-purpose-in-context.html',
              'https://webaim.org/techniques/hypertext/link_text',
            ],
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              linkHref: link.href,
              linkText: link.text,
              ariaLabel: link.ariaLabel || '',
              title: link.title || '',
              isGeneric: true,
              genericPattern: this.getMatchingGenericPattern(link.accessibleName) || '',
            },
          },
        });
      } else if (isDescriptive) {
        // Good link - add positive evidence occasionally
        if (index < 3) {
          // Only for first few to avoid too much positive evidence
          evidence.push({
            type: 'info',
            description: 'Link with descriptive text',
            value: `<a href="${link.href}">${link.accessibleName}</a>`,
            selector: link.selector,
            elementCount: 1,
            affectedSelectors: [link.selector],
            severity: 'info',
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                linkHref: link.href,
                linkText: link.text,
                ariaLabel: link.ariaLabel || '',
                isDescriptive: true,
              },
            },
          });
        }
      }
    });

    // Calculate score based on problematic links
    if (problematicLinks > 0) {
      const failureRate = problematicLinks / totalLinks;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));
    }

    // Add summary evidence
    evidence.push({
      type: score === 100 ? 'info' : 'warning',
      description: 'Link purpose analysis summary',
      value: `${totalLinks} total links, ${problematicLinks} problematic`,
      selector: 'a[href]',
      elementCount: totalLinks,
      affectedSelectors: ['a[href]'],
      severity: score === 100 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalLinks,
        checkSpecificData: {
          totalLinks,
          problematicLinks,
          successRate: (((totalLinks - problematicLinks) / totalLinks) * 100).toFixed(1),
        },
      },
    });

    // Generate recommendations
    if (problematicLinks > 0) {
      recommendations.push('Use descriptive link text that explains the purpose or destination');
      recommendations.push('Avoid generic phrases like "click here", "more", "read more"');
      recommendations.push('Ensure link text makes sense when read out of context');
      recommendations.push('Consider using aria-label for additional context when needed');
    } else {
      recommendations.push('Continue using descriptive link text');
      recommendations.push('Regularly review link text for clarity and purpose');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private isGenericLinkText(text: string): boolean {
    if (!text) return false;

    const normalizedText = text.trim().toLowerCase();
    return this.genericPatterns.some((pattern) => pattern.test(normalizedText));
  }

  private getMatchingGenericPattern(text: string): string | undefined {
    if (!text) return undefined;

    const normalizedText = text.trim().toLowerCase();
    const matchingPattern = this.genericPatterns.find((pattern) => pattern.test(normalizedText));
    return matchingPattern?.source;
  }
}
