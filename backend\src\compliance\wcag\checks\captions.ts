/**
 * WCAG-002: Captions Check (1.2.2 Level A)
 * 80% Automated - Manual review for caption accuracy and synchronization
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ManualReviewItem } from '../utils/manual-review-template';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';
import MultimediaAccessibilityTester, {
  MultimediaAccessibilityReport,
} from '../utils/multimedia-accessibility-tester';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';

export interface CaptionsConfig extends EnhancedCheckConfig {
  enableMediaAnalysis?: boolean;
  enableCaptionFileDetection?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableSynchronizationValidation?: boolean;
}

export class CaptionsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private multimediaAccessibilityTester = MultimediaAccessibilityTester.getInstance();
  private contentQualityAnalyzer = new ContentQualityAnalyzer();

  /**
   * Perform captions check - 80% automated with enhanced evidence
   */
  async performCheck(config: CaptionsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: CaptionsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableMediaAnalysis: true,
      enableCaptionFileDetection: true,
      enableAccessibilityPatterns: true,
      enableContentQualityAnalysis: true,
      enableSynchronizationValidation: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-002',
      'Captions',
      'perceivable',
      0.08,
      'A',
      enhancedConfig,
      this.executeCaptionsCheck.bind(this),
      true, // Requires browser
      true, // Manual review required
    );

    // Enhanced evidence standardization with media caption analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-002',
        ruleName: 'Captions',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.8,
          checkType: 'media-caption-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          videoAnalysis: enhancedConfig.enableMediaAnalysis,
          audioAnalysis: enhancedConfig.enableMediaAnalysis,
          captionFileDetection: enhancedConfig.enableCaptionFileDetection,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
          synchronizationValidation: enhancedConfig.enableSynchronizationValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive captions analysis with MultimediaAccessibilityTester integration
   */
  private async executeCaptionsCheck(page: Page, _config: CaptionsConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Enhanced multimedia accessibility testing
    const multimediaReport =
      await this.multimediaAccessibilityTester.testMultimediaAccessibility(page);

    // Analyze video elements with enhanced detection
    const videoAnalysis = await this.analyzeVideoElementsEnhanced(page, multimediaReport);

    // Analyze audio elements with enhanced detection
    const audioAnalysis = await this.analyzeAudioElementsEnhanced(page, multimediaReport);

    // Check for caption files with quality assessment
    const captionFileAnalysis = await this.analyzeCaptionFilesEnhanced(page);

    // Check for embedded captions with platform-specific detection
    const embeddedCaptionAnalysis = await this.analyzeEmbeddedCaptionsEnhanced(page);

    // Combine all analyses
    const allAnalyses = [
      videoAnalysis,
      audioAnalysis,
      captionFileAnalysis,
      embeddedCaptionAnalysis,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedScore = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score: automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Analyze video elements for caption requirements
   */
  private async analyzeVideoElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const videoElements = await page.$$eval('video', (videos) => {
        return videos.map((video, index) => ({
          index,
          src: video.src || '',
          poster: video.poster || '',
          controls: video.hasAttribute('controls'),
          autoplay: video.hasAttribute('autoplay'),
          muted: video.hasAttribute('muted'),
          tracks: Array.from(video.querySelectorAll('track')).map((track) => ({
            kind: track.getAttribute('kind') || '',
            src: track.getAttribute('src') || '',
            srclang: track.getAttribute('srclang') || '',
            label: track.getAttribute('label') || '',
            default: track.hasAttribute('default'),
          })),
          duration: video.duration || 0,
          hasAudio: !video.muted,
        }));
      });

      const totalChecks = videoElements.length;
      let passedChecks = 0;

      videoElements.forEach((video, index) => {
        const captionTracks = video.tracks.filter(
          (track) => track.kind === 'captions' || track.kind === 'subtitles',
        );

        if (video.hasAudio && video.duration > 0) {
          if (captionTracks.length > 0) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Video ${index + 1} has ${captionTracks.length} caption track(s)`,
              value: `video[src="${video.src}"] - tracks: ${captionTracks.length}, duration: ${video.duration}s`,
              selector: `video[src="${video.src}"]`,
              severity: 'info',
            });

            // Add manual review for caption accuracy
            manualReviewItems.push({
              selector: `video[src="${video.src}"]`,
              description: `Verify caption accuracy and synchronization for video ${index + 1}`,
              automatedFindings: `Video with audio content found, caption tracks detected`,
              reviewRequired: `Manual verification of caption accuracy, timing, and completeness`,
              priority: 'high',
              estimatedTime: 5,
              context: `Video: ${video.src}, Caption tracks: ${captionTracks.length}, Duration: ${video.duration}s`,
            });
          } else {
            issues.push(`Video ${index + 1} with audio content lacks caption tracks`);
            evidence.push({
              type: 'code',
              description: `Video ${index + 1} requires captions for accessibility`,
              value: `video[src="${video.src}"] - hasAudio: ${video.hasAudio}, duration: ${video.duration}, captionTracks: 0`,
            });
            recommendations.push(`Add caption tracks to video ${index + 1}`);
          }
        } else {
          // Video without audio or zero duration
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Video ${index + 1} does not require captions (no audio or zero duration)`,
            value: `video[src="${video.src}"] - hasAudio: ${video.hasAudio}, duration: ${video.duration}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing video elements for captions',
        value: `video - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze video elements'],
        recommendations: ['Check video elements manually for caption requirements'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze audio elements for transcript requirements
   */
  private async analyzeAudioElements(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const audioElements = await page.$$eval('audio', (audios) => {
        return audios.map((audio, index) => ({
          index,
          src: audio.src || '',
          controls: audio.hasAttribute('controls'),
          autoplay: audio.hasAttribute('autoplay'),
          muted: audio.hasAttribute('muted'),
          duration: audio.duration || 0,
        }));
      });

      const totalChecks = audioElements.length;
      let passedChecks = 0;

      audioElements.forEach((audio, index) => {
        if (audio.duration > 0 && !audio.muted) {
          // Audio content requires transcript
          manualReviewItems.push({
            selector: `audio[src="${audio.src}"]`,
            description: `Verify transcript availability for audio ${index + 1}`,
            automatedFindings: `Audio content detected with duration ${audio.duration}s`,
            reviewRequired: `Manual verification that transcript is available and accurate`,
            priority: 'high',
            estimatedTime: 3,
            context: `Audio: ${audio.src}, Duration: ${audio.duration}s`,
          });

          evidence.push({
            type: 'text',
            description: `Audio ${index + 1} requires manual verification for transcript`,
            value: `Audio duration: ${audio.duration}s, requires transcript verification`,
            selector: `audio[src="${audio.src}"]`,
            severity: 'warning',
          });
        } else {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: `Audio ${index + 1} does not require transcript (muted or zero duration)`,
            value: `audio[src="${audio.src}"] - duration: ${audio.duration}, muted: ${audio.muted}`,
          });
        }
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing audio elements for transcripts',
        value: `audio - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze audio elements'],
        recommendations: ['Check audio elements manually for transcript requirements'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze caption files (WebVTT, SRT, etc.)
   */
  private async analyzeCaptionFiles(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      const trackElements = await page.$$eval('track', (tracks) => {
        return tracks.map((track, index) => ({
          index,
          kind: track.getAttribute('kind') || '',
          src: track.getAttribute('src') || '',
          srclang: track.getAttribute('srclang') || '',
          label: track.getAttribute('label') || '',
          default: track.hasAttribute('default'),
        }));
      });

      const totalChecks = trackElements.length;
      let passedChecks = 0;

      for (const track of trackElements) {
        if (track.kind === 'captions' || track.kind === 'subtitles') {
          if (track.src) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Caption track found: ${track.label || track.srclang || 'unlabeled'}`,
              value: `track[src="${track.src}"] - kind: ${track.kind}, language: ${track.srclang}, label: ${track.label}, default: ${track.default}`,
            });

            // Add manual review for caption file quality
            manualReviewItems.push({
              selector: `track[src="${track.src}"]`,
              description: `Review caption file quality and format: ${track.src}`,
              automatedFindings: `Caption track found with kind: ${track.kind}, language: ${track.srclang}`,
              reviewRequired: `Manual verification of caption file quality, timing accuracy, and completeness`,
              priority: 'medium',
              estimatedTime: 3,
              context: `Caption: ${track.src}, Language: ${track.srclang}, Kind: ${track.kind}`,
            });
          } else {
            issues.push(`Caption track ${track.index + 1} missing src attribute`);
            evidence.push({
              type: 'code',
              description: `Caption track ${track.index + 1} has no source file`,
              value: `track[kind="${track.kind}"] - kind: ${track.kind}`,
              severity: 'error',
            });
          }
        }
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing caption files',
        value: `track - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze caption files'],
        recommendations: ['Check caption files manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Analyze embedded captions in video content
   */
  private async analyzeEmbeddedCaptions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Check for common video platforms with embedded captions
      const embeddedVideos = await page.$$eval('iframe', (iframes) => {
        return iframes
          .map((iframe, index) => ({
            index,
            src: iframe.src || '',
            title: iframe.title || '',
          }))
          .filter((iframe) => {
            const src = iframe.src.toLowerCase();
            return (
              src.includes('youtube') ||
              src.includes('vimeo') ||
              src.includes('wistia') ||
              src.includes('brightcove')
            );
          });
      });

      const totalChecks = embeddedVideos.length;
      const passedChecks = 0;

      embeddedVideos.forEach((video, index) => {
        // Embedded videos require manual verification
        manualReviewItems.push({
          selector: `iframe[src*="${video.src.split('/')[2]}"]`,
          description: `Verify embedded video captions: ${video.title || `Video ${index + 1}`}`,
          automatedFindings: `Embedded video detected from ${video.src.split('/')[2]}`,
          reviewRequired: `Manual verification that embedded video has captions enabled and accessible`,
          priority: 'high',
          estimatedTime: 4,
        });

        evidence.push({
          type: 'interaction',
          description: `Embedded video requires manual caption verification: ${video.title || `Video ${index + 1}`}`,
          value: `iframe[src="${video.src}"] - platform: ${this.detectVideoPlatform(video.src)}, requiresManualCheck: true`,
          severity: 'warning',
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing embedded captions',
        value: `iframe - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze embedded captions'],
        recommendations: ['Check embedded videos manually for captions'],
        manualReviewItems,
      };
    }
  }

  /**
   * Detect video platform from URL
   */
  private detectVideoPlatform(src: string): string {
    const url = src.toLowerCase();
    if (url.includes('youtube')) return 'YouTube';
    if (url.includes('vimeo')) return 'Vimeo';
    if (url.includes('wistia')) return 'Wistia';
    if (url.includes('brightcove')) return 'Brightcove';
    return 'Unknown';
  }

  /**
   * Enhanced video elements analysis using MultimediaAccessibilityTester
   */
  private async analyzeVideoElementsEnhanced(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    const videoElements = multimediaReport.videoElements;
    const totalChecks = videoElements.length;
    let passedChecks = 0;

    videoElements.forEach((video, index) => {
      // Enhanced caption analysis using MultimediaAccessibilityTester results
      if (video.captions.hasTextTracks && video.captions.isAccessible) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Video ${index + 1} has accessible captions`,
          value: `${video.selector} - tracks: ${video.captions.trackTypes.join(', ')}, languages: ${video.captions.languages.join(', ')}`,
          selector: video.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Video ${index + 1} lacks accessible captions`);
        evidence.push({
          type: 'code',
          description: `Video ${index + 1} requires accessible captions`,
          value: `${video.selector} - hasTextTracks: ${video.captions.hasTextTracks}, isAccessible: ${video.captions.isAccessible}`,
          selector: video.selector,
          severity: 'error',
        });
        recommendations.push(`Add accessible caption tracks to video ${index + 1}`);

        // Add manual review item for complex caption requirements
        manualReviewItems.push({
          selector: video.selector,
          description: `Manual review required for video ${index + 1} caption quality and synchronization`,
          automatedFindings: `Video lacks accessible captions`,
          reviewRequired:
            'Verify that captions are accurate, synchronized, and include all spoken content and sound effects',
          priority: 'medium',
          estimatedTime: 5,
          type: 'manual',
          element: video.selector,
        });
      }

      // Check for quality issues identified by MultimediaAccessibilityTester
      if (video.issues.length > 0) {
        video.issues.forEach((issue) => {
          issues.push(`Video ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations from MultimediaAccessibilityTester
      if (video.recommendations.length > 0) {
        video.recommendations.forEach((recommendation) => {
          recommendations.push(`Video ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
    };
  }

  /**
   * Enhanced audio elements analysis using MultimediaAccessibilityTester
   */
  private async analyzeAudioElementsEnhanced(
    page: Page,
    multimediaReport: MultimediaAccessibilityReport,
  ) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    const audioElements = multimediaReport.audioElements;
    const totalChecks = audioElements.length;
    let passedChecks = 0;

    audioElements.forEach((audio, index) => {
      // Enhanced transcript analysis
      if (audio.transcript.hasTranscript && audio.transcript.isLinked) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Audio ${index + 1} has accessible transcript`,
          value: `${audio.selector} - transcript location: ${audio.transcript.location}`,
          selector: audio.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Audio ${index + 1} lacks accessible transcript`);
        evidence.push({
          type: 'code',
          description: `Audio ${index + 1} requires accessible transcript`,
          value: `${audio.selector} - hasTranscript: ${audio.transcript.hasTranscript}, isLinked: ${audio.transcript.isLinked}`,
          selector: audio.selector,
          severity: 'error',
        });
        recommendations.push(`Add accessible transcript for audio ${index + 1}`);

        // Add manual review item for transcript quality
        manualReviewItems.push({
          selector: audio.selector,
          description: `Manual review required for audio ${index + 1} transcript quality`,
          automatedFindings: `Audio lacks accessible transcript`,
          reviewRequired: 'Verify that transcript is complete, accurate, and easily accessible',
          priority: 'medium',
          estimatedTime: 3,
          type: 'manual',
          element: audio.selector,
        });
      }

      // Check for quality issues
      if (audio.issues.length > 0) {
        audio.issues.forEach((issue) => {
          issues.push(`Audio ${index + 1}: ${issue}`);
        });
      }

      // Add recommendations
      if (audio.recommendations.length > 0) {
        audio.recommendations.forEach((recommendation) => {
          recommendations.push(`Audio ${index + 1}: ${recommendation}`);
        });
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
    };
  }

  /**
   * Enhanced caption files analysis with quality assessment
   */
  private async analyzeCaptionFilesEnhanced(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Enhanced caption file detection with quality analysis
      const captionFiles = await page.$$eval(
        'track[kind="captions"], track[kind="subtitles"]',
        (tracks) => {
          return tracks.map((track, index) => ({
            index,
            src: track.getAttribute('src') || '',
            kind: track.getAttribute('kind') || '',
            srclang: track.getAttribute('srclang') || '',
            label: track.getAttribute('label') || '',
            default: track.hasAttribute('default'),
          }));
        },
      );

      const totalChecks = captionFiles.length;
      let passedChecks = 0;

      if (captionFiles.length > 0) {
        captionFiles.forEach((track, index) => {
          // Enhanced validation using ContentQualityAnalyzer
          const hasValidSrc = track.src && track.src.trim() !== '';
          const hasValidLanguage = track.srclang && track.srclang.trim() !== '';

          if (hasValidSrc && hasValidLanguage) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: `Caption track ${index + 1} has valid source and language`,
              value: `track[src="${track.src}"] - kind: ${track.kind}, srclang: ${track.srclang}, label: ${track.label}`,
              severity: 'info',
            });
          } else {
            issues.push(`Caption track ${index + 1} has invalid or missing attributes`);
            evidence.push({
              type: 'code',
              description: `Caption track ${index + 1} requires valid src and srclang attributes`,
              value: `track - src: ${track.src}, srclang: ${track.srclang}, kind: ${track.kind}`,
              severity: 'error',
            });
            recommendations.push(`Fix caption track ${index + 1} attributes`);
          }

          // Add manual review for caption quality
          if (hasValidSrc) {
            manualReviewItems.push({
              selector: `track[src="${track.src}"]`,
              description: `Manual review required for caption file ${index + 1} quality`,
              automatedFindings: `Caption file detected with valid source`,
              reviewRequired: 'Verify caption file content is accurate, synchronized, and complete',
              priority: 'medium',
              estimatedTime: 4,
              type: 'manual',
              element: `track[src="${track.src}"]`,
            });
          }
        });
      }

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing caption files',
        value: `caption-files - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze caption files'],
        recommendations: ['Check caption files manually'],
        manualReviewItems,
      };
    }
  }

  /**
   * Enhanced embedded captions analysis with platform-specific detection
   */
  private async analyzeEmbeddedCaptionsEnhanced(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Enhanced embedded video detection with platform-specific analysis
      const embeddedVideos = await page.$$eval('iframe, embed, object', (elements) => {
        return elements
          .map((element, index) => {
            const src = element.getAttribute('src') || '';
            const title = element.getAttribute('title') || '';
            const tagName = element.tagName.toLowerCase();

            // Enhanced platform detection
            const platform = (() => {
              const srcLower = src.toLowerCase();
              if (srcLower.includes('youtube')) return 'youtube';
              if (srcLower.includes('vimeo')) return 'vimeo';
              if (srcLower.includes('wistia')) return 'wistia';
              if (srcLower.includes('brightcove')) return 'brightcove';
              if (srcLower.includes('jwplayer')) return 'jwplayer';
              if (srcLower.includes('kaltura')) return 'kaltura';
              return 'unknown';
            })();

            return {
              index,
              src,
              title,
              tagName,
              platform,
              hasTitle: title.trim() !== '',
              isVideoContent:
                src.includes('video') || src.includes('embed') || platform !== 'unknown',
            };
          })
          .filter((element) => element.isVideoContent);
      });

      const totalChecks = embeddedVideos.length;
      const passedChecks = 0;

      embeddedVideos.forEach((video) => {
        // Platform-specific caption guidance
        const platformGuidance = this.getPlatformCaptionGuidance(video.platform);

        if (video.hasTitle) {
          evidence.push({
            type: 'text',
            description: `Embedded video ${video.index + 1} has descriptive title`,
            value: `${video.tagName}[src="${video.src}"] - title: ${video.title}, platform: ${video.platform}`,
            severity: 'info',
          });
        } else {
          issues.push(`Embedded video ${video.index + 1} lacks descriptive title`);
          evidence.push({
            type: 'code',
            description: `Embedded video ${video.index + 1} requires descriptive title`,
            value: `${video.tagName}[src="${video.src}"] - platform: ${video.platform}`,
            severity: 'warning',
          });
          recommendations.push(`Add descriptive title to embedded video ${video.index + 1}`);
        }

        // Add platform-specific manual review
        manualReviewItems.push({
          selector: `${video.tagName}[src="${video.src}"]`,
          description: `Manual review required for ${video.platform} embedded video ${video.index + 1}`,
          automatedFindings: `Embedded ${video.platform} video detected`,
          reviewRequired: platformGuidance,
          priority: 'high',
          estimatedTime: 8,
          type: 'manual',
          element: `${video.tagName}[src="${video.src}"]`,
        });
      });

      return {
        totalChecks,
        passedChecks,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
      };
    } catch (error) {
      evidence.push({
        type: 'text',
        description: 'Error analyzing embedded captions',
        value: `embedded-captions - error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });

      return {
        totalChecks: 1,
        passedChecks: 0,
        evidence,
        issues: ['Failed to analyze embedded captions'],
        recommendations: ['Check embedded videos manually for caption availability'],
        manualReviewItems,
      };
    }
  }

  /**
   * Get platform-specific caption guidance
   */
  private getPlatformCaptionGuidance(platform: string): string {
    const guidance: Record<string, string> = {
      youtube:
        'Verify that YouTube video has accurate captions enabled. Check auto-generated captions for accuracy and consider uploading custom caption files.',
      vimeo:
        'Ensure Vimeo video has captions uploaded. Vimeo supports WebVTT caption files that can be uploaded through their interface.',
      wistia:
        'Check that Wistia video has captions configured. Wistia provides caption editing tools and supports multiple caption formats.',
      brightcove:
        'Verify Brightcove video has caption tracks configured. Check the video metadata for caption availability.',
      jwplayer:
        'Ensure JW Player video has caption tracks loaded. Verify caption files are properly configured in the player setup.',
      kaltura:
        'Check that Kaltura video has caption tracks available. Verify caption configuration in the Kaltura management console.',
      unknown:
        'Manually verify that this embedded video content has captions or alternative text available. Contact the content provider if captions are missing.',
    };

    return guidance[platform] || guidance.unknown;
  }
}
