// backend/src/config/performance.ts

/**
 * Performance optimization configuration for HIPAA compliance analysis
 * Optimized for VPS with 4 core CPU and 8GB RAM
 */

export interface PerformanceConfig {
  // Analysis concurrency limits
  maxConcurrentAnalyses: number;
  maxConcurrentChecks: number;

  // Memory management
  maxMemoryUsageMB: number;
  gcThresholdMB: number;

  // Timeout configurations
  analysisTimeoutMs: number;
  networkTimeoutMs: number;
  databaseTimeoutMs: number;

  // Caching settings
  enableResultCaching: boolean;
  cacheExpiryMinutes: number;
  maxCacheSize: number;

  // AI/NLP optimization
  aiModelConcurrency: number;
  nlpBatchSize: number;
  contentChunkSize: number;

  // Database optimization
  connectionPoolSize: number;
  queryTimeoutMs: number;
  batchInsertSize: number;
}

/**
 * Production-optimized configuration for VPS deployment
 */
export const PRODUCTION_CONFIG: PerformanceConfig = {
  // Conservative concurrency for 4-core CPU
  maxConcurrentAnalyses: 2,
  maxConcurrentChecks: 4,

  // Memory management for 8GB RAM (use ~60% max)
  maxMemoryUsageMB: 4800,
  gcThresholdMB: 3200,

  // Reasonable timeouts for production
  analysisTimeoutMs: 120000, // 2 minutes
  networkTimeoutMs: 30000, // 30 seconds
  databaseTimeoutMs: 15000, // 15 seconds

  // Enable caching for performance
  enableResultCaching: true,
  cacheExpiryMinutes: 60,
  maxCacheSize: 1000,

  // AI/NLP optimization for resource constraints
  aiModelConcurrency: 1, // Single model instance
  nlpBatchSize: 10, // Small batches
  contentChunkSize: 2048, // 2KB chunks

  // Database optimization
  connectionPoolSize: 8,
  queryTimeoutMs: 10000,
  batchInsertSize: 50,
};

/**
 * Development configuration for local testing
 */
export const DEVELOPMENT_CONFIG: PerformanceConfig = {
  maxConcurrentAnalyses: 1,
  maxConcurrentChecks: 2,

  maxMemoryUsageMB: 2048,
  gcThresholdMB: 1024,

  analysisTimeoutMs: 60000,
  networkTimeoutMs: 15000,
  databaseTimeoutMs: 10000,

  enableResultCaching: false, // Disable for development
  cacheExpiryMinutes: 5,
  maxCacheSize: 100,

  aiModelConcurrency: 1,
  nlpBatchSize: 5,
  contentChunkSize: 1024,

  connectionPoolSize: 4,
  queryTimeoutMs: 5000,
  batchInsertSize: 20,
};

/**
 * Test configuration for CI/CD
 */
export const TEST_CONFIG: PerformanceConfig = {
  maxConcurrentAnalyses: 1,
  maxConcurrentChecks: 1,

  maxMemoryUsageMB: 512,
  gcThresholdMB: 256,

  analysisTimeoutMs: 30000,
  networkTimeoutMs: 5000,
  databaseTimeoutMs: 5000,

  enableResultCaching: false,
  cacheExpiryMinutes: 1,
  maxCacheSize: 10,

  aiModelConcurrency: 1,
  nlpBatchSize: 2,
  contentChunkSize: 512,

  connectionPoolSize: 2,
  queryTimeoutMs: 3000,
  batchInsertSize: 5,
};

/**
 * Get performance configuration based on environment
 */
export function getPerformanceConfig(): PerformanceConfig {
  const env = process.env.NODE_ENV || 'development';

  switch (env) {
    case 'production':
      return PRODUCTION_CONFIG;
    case 'test':
      return TEST_CONFIG;
    default:
      return DEVELOPMENT_CONFIG;
  }
}

/**
 * Memory monitoring utilities
 */
export class MemoryMonitor {
  private static lastGC = Date.now();

  static getCurrentMemoryUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  }

  static getMemoryUsageMB(): number {
    const usage = process.memoryUsage();
    return Math.round(usage.heapUsed / 1024 / 1024);
  }

  static shouldTriggerGC(config: PerformanceConfig): boolean {
    const currentUsage = this.getMemoryUsageMB();
    const timeSinceLastGC = Date.now() - this.lastGC;

    return (
      currentUsage > config.gcThresholdMB && timeSinceLastGC > 30000 // At least 30 seconds since last GC
    );
  }

  static triggerGC(): void {
    if (global.gc) {
      global.gc();
      this.lastGC = Date.now();
    }
  }

  static logMemoryStats(): void {
    const usage = process.memoryUsage();
    console.log('Memory Usage:', {
      heapUsed: `${Math.round(usage.heapUsed / 1024 / 1024)}MB`,
      heapTotal: `${Math.round(usage.heapTotal / 1024 / 1024)}MB`,
      external: `${Math.round(usage.external / 1024 / 1024)}MB`,
      rss: `${Math.round(usage.rss / 1024 / 1024)}MB`,
    });
  }
}

/**
 * Performance metrics collection
 */
export class PerformanceMetrics {
  private static metrics: Map<string, number[]> = new Map();

  static recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const values = this.metrics.get(name)!;
    values.push(value);

    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  static getAverageMetric(name: string): number {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) return 0;

    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  static getMetricSummary(name: string): { avg: number; min: number; max: number; count: number } {
    const values = this.metrics.get(name);
    if (!values || values.length === 0) {
      return { avg: 0, min: 0, max: 0, count: 0 };
    }

    return {
      avg: values.reduce((sum, val) => sum + val, 0) / values.length,
      min: Math.min(...values),
      max: Math.max(...values),
      count: values.length,
    };
  }

  static getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, { avg: number; min: number; max: number; count: number }> = {};

    for (const [name] of Array.from(this.metrics)) {
      result[name] = this.getMetricSummary(name);
    }

    return result;
  }

  static clearMetrics(): void {
    this.metrics.clear();
  }
}

export default {
  getPerformanceConfig,
  MemoryMonitor,
  PerformanceMetrics,
  PRODUCTION_CONFIG,
  DEVELOPMENT_CONFIG,
  TEST_CONFIG,
};
