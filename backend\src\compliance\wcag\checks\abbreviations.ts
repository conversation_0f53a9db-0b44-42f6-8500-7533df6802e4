/**
 * WCAG-061: Abbreviations Check (3.1.4 Level AAA)
 * 75% Automated - Detects abbreviations without expanded forms
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface AbbreviationsConfig extends EnhancedCheckConfig {
  enableAILanguageDetection?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableAISemanticValidation?: boolean;
  enableAbbreviationDetection?: boolean;
  enableExpansionValidation?: boolean;
}

export class AbbreviationsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  // Common abbreviations that should have expansions
  private readonly commonAbbreviations = new Set([
    'etc',
    'vs',
    'ie',
    'eg',
    'cf',
    'et al',
    'ibid',
    'op cit',
    'loc cit',
    'viz',
    'nb',
    'ps',
    'pps',
    'mr',
    'mrs',
    'ms',
    'dr',
    'prof',
    'rev',
    'hon',
    'jr',
    'sr',
    'st',
    'ave',
    'blvd',
    'rd',
    'ln',
    'jan',
    'feb',
    'mar',
    'apr',
    'may',
    'jun',
    'jul',
    'aug',
    'sep',
    'oct',
    'nov',
    'dec',
    'mon',
    'tue',
    'wed',
    'thu',
    'fri',
    'sat',
    'sun',
    'am',
    'pm',
    'ad',
    'bc',
    'ce',
    'bce',
    'ca',
    'circa',
    'usa',
    'uk',
    'eu',
    'un',
    'nato',
    'nasa',
    'fbi',
    'cia',
    'nsa',
    'irs',
    'fda',
    'epa',
    'ceo',
    'cfo',
    'cto',
    'coo',
    'hr',
    'pr',
    'it',
    'qa',
    'rd',
    'rnd',
    'html',
    'css',
    'js',
    'xml',
    'json',
    'api',
    'url',
    'uri',
    'http',
    'https',
    'ftp',
    'ssh',
    'sql',
    'nosql',
    'crud',
    'rest',
    'soap',
    'ajax',
    'dom',
    'mvc',
    'mvp',
    'mvvm',
    'ui',
    'ux',
    'gui',
    'cli',
    'ide',
    'sdk',
    'api',
    'cdn',
    'dns',
    'ssl',
    'tls',
    'vpn',
    'saas',
    'paas',
    'iaas',
    'crm',
    'erp',
    'cms',
    'lms',
    'dms',
    'bpm',
    'bi',
    'ai',
    'ml',
    'iot',
  ]);

  async performCheck(config: AbbreviationsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: AbbreviationsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAILanguageDetection: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-061',
      'Abbreviations',
      'understandable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeAbbreviationsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with abbreviation analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-061',
        ruleName: 'Abbreviations',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'abbreviation-analysis',
          abbreviationDetection: true,
          expansionValidation: true,
          aiLanguageDetection: enhancedConfig.enableAILanguageDetection,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 35,
      },
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeAbbreviationsCheck(
    page: Page,
    config: AbbreviationsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze abbreviations in content
    const abbreviationAnalysis = await page.evaluate(() => {
      const textElements = document.querySelectorAll(
        'p, div, span, h1, h2, h3, h4, h5, h6, li, td, th, article, section, main, aside',
      );
      const abbreviations: Array<{
        abbreviation: string;
        context: string;
        element: string;
        selector: string;
        hasAbbrTag: boolean;
        hasTitle: boolean;
        hasExpansion: boolean;
        hasGlossary: boolean;
        expandedForm: string;
        frequency: number;
      }> = [];

      const abbrFrequency = new Map<string, number>();
      const abbrContexts = new Map<
        string,
        Array<{ context: string; element: string; selector: string; hasExpansion: boolean }>
      >();

      // Common abbreviation patterns
      const abbreviationPatterns = [
        // Acronyms (2+ capital letters)
        /\b[A-Z]{2,}\b/g,
        // Common abbreviations with periods
        /\b(?:etc|vs|ie|eg|cf|et al|ibid|op cit|loc cit|viz|nb|ps|pps)\./gi,
        // Title abbreviations
        /\b(?:Mr|Mrs|Ms|Dr|Prof|Rev|Hon|Jr|Sr)\./gi,
        // Location abbreviations
        /\b(?:St|Ave|Blvd|Rd|Ln)\./gi,
        // Time abbreviations
        /\b(?:am|pm|AD|BC|CE|BCE|ca|circa)\b/gi,
        // Month abbreviations
        /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\./gi,
        // Day abbreviations
        /\b(?:Mon|Tue|Wed|Thu|Fri|Sat|Sun)\./gi,
      ];

      textElements.forEach((element, index) => {
        const text = element.textContent || '';
        if (text.trim().length < 5) return; // Skip very short text

        const selector = generateSelector(element, index);

        abbreviationPatterns.forEach((pattern) => {
          let match;
          while ((match = pattern.exec(text)) !== null) {
            const abbr = match[0].replace(/\.$/, ''); // Remove trailing period
            const abbrLower = abbr.toLowerCase();

            // Skip single letters and very common words
            if (abbr.length < 2 || isCommonWord(abbrLower)) continue;

            // Check if abbreviation has proper markup
            const hasAbbrTag = element.querySelector(`abbr[title], acronym[title]`) !== null;
            const hasTitle = element.hasAttribute('title');

            // Check for expansion in text
            const hasExpansion = checkForExpansion(text, abbr);
            const hasGlossary = checkForGlossaryLink(element, abbr);
            const expandedForm = findExpandedForm(text, abbr);

            // Get context (surrounding text)
            const abbrIndex = text.indexOf(abbr);
            const contextStart = Math.max(0, abbrIndex - 40);
            const contextEnd = Math.min(text.length, abbrIndex + abbr.length + 40);
            const context = text.substring(contextStart, contextEnd).trim();

            // Track frequency
            const currentFreq = abbrFrequency.get(abbrLower) || 0;
            abbrFrequency.set(abbrLower, currentFreq + 1);

            // Track contexts
            if (!abbrContexts.has(abbrLower)) {
              abbrContexts.set(abbrLower, []);
            }
            abbrContexts.get(abbrLower)!.push({
              context,
              element: element.tagName.toLowerCase(),
              selector,
              hasExpansion,
            });
          }
        });
      });

      // Process collected abbreviations
      abbrFrequency.forEach((frequency, abbr) => {
        const contexts = abbrContexts.get(abbr) || [];
        const firstContext = contexts[0];

        if (firstContext) {
          const element = document.querySelector(firstContext.selector);
          const hasAbbrTag = element
            ? element.querySelector(`abbr[title*="${abbr}"], acronym[title*="${abbr}"]`) !== null
            : false;
          const hasTitle = element ? element.hasAttribute('title') : false;
          const hasGlossary = element ? checkForGlossaryLink(element, abbr) : false;
          const expandedForm = findExpandedForm(firstContext.context, abbr);

          abbreviations.push({
            abbreviation: abbr,
            context: firstContext.context,
            element: firstContext.element,
            selector: firstContext.selector,
            hasAbbrTag,
            hasTitle,
            hasExpansion: firstContext.hasExpansion,
            hasGlossary,
            expandedForm,
            frequency,
          });
        }
      });

      return {
        abbreviations,
        totalTextElements: textElements.length,
        totalAbbreviations: Array.from(abbrFrequency.values()).reduce((sum, freq) => sum + freq, 0),
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter((c) => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function isCommonWord(word: string): boolean {
        const commonWords = new Set([
          'a',
          'an',
          'and',
          'are',
          'as',
          'at',
          'be',
          'by',
          'for',
          'from',
          'has',
          'he',
          'in',
          'is',
          'it',
          'its',
          'of',
          'on',
          'that',
          'the',
          'to',
          'was',
          'will',
          'with',
        ]);
        return commonWords.has(word) || word.length < 2;
      }

      function checkForExpansion(text: string, abbr: string): boolean {
        // Check for common expansion patterns
        const expansionPatterns = [
          new RegExp(`${abbr}\\s*\\([^)]+\\)`, 'i'), // Abbr (expansion)
          new RegExp(`\\([^)]*${abbr}[^)]*\\)`, 'i'), // (expansion abbr)
          new RegExp(`${abbr}\\s*(?:stands for|means|is|refers to)\\s+[^.]+`, 'i'),
          new RegExp(`[^.]+\\s+\\(${abbr}\\)`, 'i'), // Full form (abbr)
        ];

        return expansionPatterns.some((pattern) => pattern.test(text));
      }

      function findExpandedForm(text: string, abbr: string): string {
        // Try to extract the expanded form
        const patterns = [
          new RegExp(`${abbr}\\s*\\(([^)]+)\\)`, 'i'),
          new RegExp(`([^(]+)\\s*\\(${abbr}\\)`, 'i'),
          new RegExp(`${abbr}\\s*(?:stands for|means|is|refers to)\\s+([^.]+)`, 'i'),
        ];

        for (const pattern of patterns) {
          const match = text.match(pattern);
          if (match && match[1]) {
            return match[1].trim();
          }
        }

        return '';
      }

      function checkForGlossaryLink(element: Element, abbr: string): boolean {
        const links = element.querySelectorAll('a[href]');
        return Array.from(links).some((link) => {
          const href = link.getAttribute('href') || '';
          const linkText = link.textContent || '';
          return (
            (href.includes('glossary') || href.includes('definition') || href.includes('terms')) &&
            linkText.toLowerCase().includes(abbr.toLowerCase())
          );
        });
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = abbreviationAnalysis.abbreviations.length;

    if (elementCount > 0) {
      // Filter abbreviations that don't have expansions
      const abbreviationsWithoutExpansions = abbreviationAnalysis.abbreviations.filter(
        (abbr) => !abbr.hasAbbrTag && !abbr.hasExpansion && !abbr.hasGlossary && !abbr.expandedForm,
      );

      if (abbreviationsWithoutExpansions.length > 0) {
        // Calculate score based on number of unexpanded abbreviations
        const penalty = Math.min(50, abbreviationsWithoutExpansions.length * 8);
        score -= penalty;

        issues.push(`${abbreviationsWithoutExpansions.length} abbreviations lack expanded forms`);

        evidence.push({
          type: 'content',
          description: 'Abbreviations without expanded forms',
          value: `Found ${abbreviationsWithoutExpansions.length} abbreviations that need expanded forms for AAA compliance`,
          elementCount: abbreviationsWithoutExpansions.length,
          affectedSelectors: abbreviationsWithoutExpansions.map((abbr) => abbr.selector),
          severity: 'warning',
          fixExample: {
            before: 'Contact our CEO for more information.',
            after:
              'Contact our <abbr title="Chief Executive Officer">CEO</abbr> for more information.',
            description:
              'Provide expanded forms for abbreviations using abbr elements or inline expansions',
            codeExample: `
<!-- Before: Unexpanded abbreviations -->
<p>Our API supports JSON and XML formats. Contact our CEO or CTO for details.</p>

<!-- After: With abbr elements -->
<p>Our <abbr title="Application Programming Interface">API</abbr> supports 
<abbr title="JavaScript Object Notation">JSON</abbr> and 
<abbr title="eXtensible Markup Language">XML</abbr> formats. 
Contact our <abbr title="Chief Executive Officer">CEO</abbr> or 
<abbr title="Chief Technology Officer">CTO</abbr> for details.</p>

<!-- Alternative: With inline expansions -->
<p>Our API (Application Programming Interface) supports JSON (JavaScript Object Notation) 
and XML (eXtensible Markup Language) formats. Contact our CEO (Chief Executive Officer) 
or CTO (Chief Technology Officer) for details.</p>

<!-- Alternative: With glossary links -->
<p>Our <a href="/glossary#api">API</a> supports <a href="/glossary#json">JSON</a> 
and <a href="/glossary#xml">XML</a> formats.</p>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/abbreviations.html',
              'https://developer.mozilla.org/en-US/docs/Web/HTML/Element/abbr',
              'https://www.w3.org/WAI/WCAG21/Techniques/html/H28',
            ],
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: abbreviationAnalysis.totalTextElements,
            checkSpecificData: {
              totalAbbreviations: elementCount,
              abbreviationsWithoutExpansions: abbreviationsWithoutExpansions.length,
              abbreviationsWithExpansions: elementCount - abbreviationsWithoutExpansions.length,
              mostFrequentAbbreviations: abbreviationAnalysis.abbreviations
                .sort((a, b) => b.frequency - a.frequency)
                .slice(0, 5)
                .map((a) => ({ abbreviation: a.abbreviation, frequency: a.frequency })),
            },
          },
        });

        // Add specific examples for abbreviations without expansions
        abbreviationsWithoutExpansions.slice(0, 10).forEach((abbrInfo) => {
          evidence.push({
            type: 'content',
            description: `Abbreviation without expansion: "${abbrInfo.abbreviation}"`,
            value: `Context: "${abbrInfo.context}"`,
            selector: abbrInfo.selector,
            severity: 'warning',
            metadata: {
              checkSpecificData: {
                abbreviation: abbrInfo.abbreviation,
                frequency: abbrInfo.frequency,
                hasAbbrTag: abbrInfo.hasAbbrTag,
                hasExpansion: abbrInfo.hasExpansion,
                hasGlossary: abbrInfo.hasGlossary,
                expandedForm: abbrInfo.expandedForm,
              },
            },
          });
        });

        recommendations.push('Use <abbr> elements with title attributes for abbreviations');
        recommendations.push('Provide inline expansions for abbreviations on first use');
        recommendations.push('Create a glossary page for frequently used abbreviations');
        recommendations.push('Consider the reading level and technical knowledge of your audience');
        recommendations.push('Ensure abbreviations are consistently expanded throughout the site');
      } else {
        // All abbreviations have expansions
        evidence.push({
          type: 'info',
          description: 'Abbreviations with expansions found',
          value: `Found ${elementCount} abbreviations, all with appropriate expanded forms`,
          elementCount,
          severity: 'info',
          metadata: {
            scanDuration,
            elementsAnalyzed: abbreviationAnalysis.totalTextElements,
            checkSpecificData: {
              totalAbbreviations: elementCount,
              allHaveExpansions: true,
            },
          },
        });
      }
    } else {
      // No abbreviations found
      evidence.push({
        type: 'info',
        description: 'No abbreviations detected',
        value: 'Page content does not contain abbreviations requiring expansion',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: abbreviationAnalysis.totalTextElements,
          checkSpecificData: {
            noAbbreviations: true,
            totalAbbreviations: abbreviationAnalysis.totalAbbreviations,
          },
        },
      });
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
