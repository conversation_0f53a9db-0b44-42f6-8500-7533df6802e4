/**
 * WCAG-059: Concurrent Input Mechanisms Check (2.5.6 Level AAA)
 * 65% Automated - Detects restrictions on input mechanisms
 */

import { Page } from 'puppeteer';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { ModernFrameworkOptimizer } from '../utils/modern-framework-optimizer';
import { ComponentLibraryDetector } from '../utils/component-library-detector';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface InputModalityDetection {
  touchRestrictions: number;
  mouseRestrictions: number;
  keyboardRestrictions: number;
  pointerRestrictions: number;
  totalRestrictions: number;
  restrictionTypes: string[];
  riskLevel: 'none' | 'low' | 'medium' | 'high';
  detectionConfidence: number;
}

interface RestrictionAnalysis {
  hasInputRestrictions: boolean;
  restrictionMethods: string[];
  affectedElements: number;
  severityLevel: 'low' | 'medium' | 'high';
  bypassMechanisms: string[];
}

interface MultiModalInteractionTesting {
  supportsTouch: boolean;
  supportsMouse: boolean;
  supportsKeyboard: boolean;
  supportsPointer: boolean;
  concurrentSupport: boolean;
  interactionScore: number;
  compatibilityRating: 'excellent' | 'good' | 'fair' | 'poor';
}

export interface ConcurrentInputMechanismsConfig extends EnhancedCheckConfig {
  enableInputModalityDetection?: boolean;
  enableRestrictionAnalysis?: boolean;
  enableMultiModalInteractionTesting?: boolean;
  enableAccessibilityInputAssessment?: boolean;
  enableAdvancedInputTracking?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableModernFrameworkOptimization?: boolean;
}

export class ConcurrentInputMechanismsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private modernFrameworkOptimizer = ModernFrameworkOptimizer.getInstance();
  private componentLibraryDetector = ComponentLibraryDetector.getInstance();

  async performCheck(config: ConcurrentInputMechanismsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with specialized input modality detection
    const enhancedConfig: ConcurrentInputMechanismsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 2000, // Target: <2s performance
      },
      enableInputModalityDetection: true,
      enableRestrictionAnalysis: true,
      enableMultiModalInteractionTesting: true,
      enableAccessibilityInputAssessment: true,
      enableAdvancedInputTracking: true,
      enableAccessibilityPatterns: true,
      enableModernFrameworkOptimization: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-059',
      'Concurrent Input Mechanisms',
      'operable',
      0.0305,
      'AAA',
      enhancedConfig,
      this.executeConcurrentInputMechanismsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with input mechanism analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-059',
        ruleName: 'Concurrent Input Mechanisms',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.65,
          checkType: 'input-mechanism-analysis',
          inputRestrictionDetection: true,
          multiModalSupport: true,
          advancedInputTracking: enhancedConfig.enableAdvancedInputTracking,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  private async executeConcurrentInputMechanismsCheck(
    page: Page,
    _config: ConcurrentInputMechanismsConfig,
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Specialized Input Modality Detection Algorithm - Advanced Implementation
    const inputModalityDetection = await this.executeInputModalityDetection(page);

    // Restriction Analysis Algorithm
    const restrictionAnalysis = await this.analyzeInputRestrictions(page);

    // Multi-Modal Interaction Testing Algorithm
    const multiModalTesting = await this.testMultiModalInteractions(page);

    // Accessibility Input Assessment Algorithm
    const accessibilityInputAssessment = await this.assessAccessibilityInputs(page);

    // Combine all specialized detection results
    const allAnalyses = [
      inputModalityDetection,
      restrictionAnalysis,
      multiModalTesting,
      accessibilityInputAssessment,
    ];

    let totalChecks = 0;
    let passedChecks = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
    });

    // Calculate score with 75% accuracy target
    const score = totalChecks > 0 ? Math.round((passedChecks / totalChecks) * 100) : 100;

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Input Modality Detection Algorithm - Core Implementation
   * Target: 75% input modality restriction detection accuracy
   */
  private async executeInputModalityDetection(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const modalityDetection = await page.evaluate((): InputModalityDetection => {
      // Advanced input modality detection
      let touchRestrictions = 0;
      let mouseRestrictions = 0;
      let keyboardRestrictions = 0;
      let pointerRestrictions = 0;
      const restrictionTypes: string[] = [];

      // Check all elements for input restrictions
      const allElements = Array.from(document.querySelectorAll('*'));

      allElements.forEach(element => {
        const computedStyle = window.getComputedStyle(element);

        // Check touch restrictions
        const touchAction = computedStyle.touchAction;
        if (touchAction === 'none') {
          touchRestrictions++;
          restrictionTypes.push('touch-disabled');
        } else if (touchAction && touchAction !== 'auto' && touchAction !== 'manipulation') {
          touchRestrictions++;
          restrictionTypes.push('touch-restricted');
        }

        // Check pointer restrictions
        const pointerEvents = computedStyle.pointerEvents;
        if (pointerEvents === 'none') {
          pointerRestrictions++;
          restrictionTypes.push('pointer-disabled');
        }

        // Check for mouse-only interactions
        const hasMouseOnly = element.hasAttribute('onmousedown') &&
                            !element.hasAttribute('ontouchstart') &&
                            !element.hasAttribute('onpointerdown');
        if (hasMouseOnly) {
          mouseRestrictions++;
          restrictionTypes.push('mouse-only');
        }

        // Check for keyboard restrictions
        const tabIndex = element.getAttribute('tabindex');
        if (tabIndex === '-1' && element.hasAttribute('onclick')) {
          keyboardRestrictions++;
          restrictionTypes.push('keyboard-disabled');
        }
      });

      // Check for CSS that prevents certain input types
      const stylesheets = Array.from(document.styleSheets);
      stylesheets.forEach(stylesheet => {
        try {
          const rules = Array.from(stylesheet.cssRules || []);
          rules.forEach(rule => {
            const cssText = rule.cssText.toLowerCase();
            if (cssText.includes('touch-action: none')) {
              touchRestrictions++;
              restrictionTypes.push('css-touch-disabled');
            }
            if (cssText.includes('pointer-events: none')) {
              pointerRestrictions++;
              restrictionTypes.push('css-pointer-disabled');
            }
          });
        } catch (e) {
          // Cross-origin stylesheet access might fail
        }
      });

      const totalRestrictions = touchRestrictions + mouseRestrictions + keyboardRestrictions + pointerRestrictions;

      // Calculate risk level
      let riskLevel: InputModalityDetection['riskLevel'] = 'none';
      let detectionConfidence = 0.75;

      if (totalRestrictions === 0) {
        riskLevel = 'none';
        detectionConfidence = 0.8;
      } else if (totalRestrictions <= 3) {
        riskLevel = 'low';
        detectionConfidence = 0.75;
      } else if (totalRestrictions <= 8) {
        riskLevel = 'medium';
        detectionConfidence = 0.7;
      } else {
        riskLevel = 'high';
        detectionConfidence = 0.65;
      }

      return {
        touchRestrictions,
        mouseRestrictions,
        keyboardRestrictions,
        pointerRestrictions,
        totalRestrictions,
        restrictionTypes: [...new Set(restrictionTypes)],
        riskLevel,
        detectionConfidence,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (modalityDetection.riskLevel === 'none' || modalityDetection.riskLevel === 'low') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Input modality detection: Low or no input restrictions detected',
        value: `Risk level: ${modalityDetection.riskLevel}, Confidence: ${(modalityDetection.detectionConfidence * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Input modality restrictions: ${modalityDetection.riskLevel} risk detected`);
      evidence.push({
        type: 'code',
        description: `Input modality detection: ${modalityDetection.riskLevel} risk`,
        value: `Touch: ${modalityDetection.touchRestrictions}, Mouse: ${modalityDetection.mouseRestrictions}, Keyboard: ${modalityDetection.keyboardRestrictions}, Pointer: ${modalityDetection.pointerRestrictions}`,
        severity: modalityDetection.riskLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Remove or reduce input modality restrictions to support concurrent input mechanisms');
    }

    // Report detected restriction types
    if (modalityDetection.restrictionTypes.length > 0) {
      evidence.push({
        type: 'text',
        description: 'Detected input restriction types',
        value: modalityDetection.restrictionTypes.slice(0, 5).join(', '),
        severity: 'info',
      });
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Restriction Analysis Algorithm
   */
  private async analyzeInputRestrictions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const restrictionAnalysis = await page.evaluate((): RestrictionAnalysis => {
      const restrictionMethods: string[] = [];
      let affectedElements = 0;
      const bypassMechanisms: string[] = [];

      // Check for CSS-based restrictions
      const elementsWithRestrictions = Array.from(document.querySelectorAll('*')).filter(element => {
        const style = window.getComputedStyle(element);
        const hasRestriction = style.touchAction === 'none' ||
                              style.pointerEvents === 'none' ||
                              style.userSelect === 'none';

        if (hasRestriction) {
          affectedElements++;
          if (style.touchAction === 'none') restrictionMethods.push('touch-action-none');
          if (style.pointerEvents === 'none') restrictionMethods.push('pointer-events-none');
          if (style.userSelect === 'none') restrictionMethods.push('user-select-none');
        }

        return hasRestriction;
      });

      // Check for JavaScript-based restrictions
      const scriptsWithRestrictions = Array.from(document.querySelectorAll('script')).filter(script => {
        const content = script.textContent || '';
        const hasInputRestriction = content.includes('preventDefault') &&
                                   (content.includes('touch') || content.includes('mouse') || content.includes('pointer'));

        if (hasInputRestriction) {
          restrictionMethods.push('javascript-prevention');
        }

        return hasInputRestriction;
      });

      // Check for bypass mechanisms
      const settingsElements = document.querySelectorAll('.settings, .preferences, .accessibility, [data-accessibility]');
      if (settingsElements.length > 0) {
        bypassMechanisms.push('settings-panel');
      }

      const toggleElements = document.querySelectorAll('.toggle-input, .input-mode, [data-input-toggle]');
      if (toggleElements.length > 0) {
        bypassMechanisms.push('input-toggle');
      }

      const hasInputRestrictions = restrictionMethods.length > 0;

      // Determine severity level
      let severityLevel: RestrictionAnalysis['severityLevel'] = 'low';
      if (affectedElements > 20 || restrictionMethods.length > 5) {
        severityLevel = 'high';
      } else if (affectedElements > 5 || restrictionMethods.length > 2) {
        severityLevel = 'medium';
      }

      return {
        hasInputRestrictions,
        restrictionMethods: [...new Set(restrictionMethods)],
        affectedElements,
        severityLevel,
        bypassMechanisms,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (!restrictionAnalysis.hasInputRestrictions ||
        (restrictionAnalysis.severityLevel === 'low' && restrictionAnalysis.bypassMechanisms.length > 0)) {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Restriction analysis: No significant input restrictions or adequate bypass mechanisms',
        value: `Severity: ${restrictionAnalysis.severityLevel}, Bypass mechanisms: ${restrictionAnalysis.bypassMechanisms.length}`,
        severity: 'info',
      });
    } else {
      issues.push(`Input restrictions detected: ${restrictionAnalysis.severityLevel} severity`);
      evidence.push({
        type: 'code',
        description: `Restriction analysis: ${restrictionAnalysis.severityLevel} severity restrictions`,
        value: `Methods: ${restrictionAnalysis.restrictionMethods.join(', ')}, Affected elements: ${restrictionAnalysis.affectedElements}, Bypass mechanisms: ${restrictionAnalysis.bypassMechanisms.length}`,
        severity: restrictionAnalysis.severityLevel === 'high' ? 'error' : 'warning',
      });
      recommendations.push('Add bypass mechanisms for input restrictions or reduce restriction severity');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Multi-Modal Interaction Testing Algorithm
   */
  private async testMultiModalInteractions(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const multiModalTesting = await page.evaluate((): MultiModalInteractionTesting => {
      // Test support for different input modalities
      const supportsTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const supportsMouse = 'onmousedown' in window;
      const supportsKeyboard = 'onkeydown' in window;
      const supportsPointer = 'onpointerdown' in window;

      // Check for concurrent support in interactive elements
      const interactiveElements = Array.from(document.querySelectorAll(
        'button, [role="button"], input, select, textarea, a[href], [tabindex]:not([tabindex="-1"])'
      ));

      let concurrentSupportCount = 0;

      interactiveElements.forEach(element => {
        const hasTouch = element.hasAttribute('ontouchstart') || element.hasAttribute('ontouchend');
        const hasMouse = element.hasAttribute('onmousedown') || element.hasAttribute('onclick');
        const hasKeyboard = element.hasAttribute('onkeydown') || element.hasAttribute('tabindex');
        const hasPointer = element.hasAttribute('onpointerdown') || element.hasAttribute('onpointerup');

        // Count elements that support multiple input methods
        const supportCount = [hasTouch, hasMouse, hasKeyboard, hasPointer].filter(Boolean).length;
        if (supportCount >= 2) {
          concurrentSupportCount++;
        }
      });

      const concurrentSupport = interactiveElements.length > 0 ?
        concurrentSupportCount / interactiveElements.length >= 0.8 : true;

      // Calculate interaction score (0-1)
      const modalitySupport = [supportsTouch, supportsMouse, supportsKeyboard, supportsPointer].filter(Boolean).length;
      const interactionScore = (modalitySupport / 4) * (concurrentSupport ? 1 : 0.5);

      // Determine compatibility rating
      let compatibilityRating: MultiModalInteractionTesting['compatibilityRating'] = 'poor';
      if (interactionScore >= 0.9) {
        compatibilityRating = 'excellent';
      } else if (interactionScore >= 0.7) {
        compatibilityRating = 'good';
      } else if (interactionScore >= 0.5) {
        compatibilityRating = 'fair';
      }

      return {
        supportsTouch,
        supportsMouse,
        supportsKeyboard,
        supportsPointer,
        concurrentSupport,
        interactionScore,
        compatibilityRating,
      };
    });

    const totalChecks = 1;
    let passedChecks = 0;

    if (multiModalTesting.compatibilityRating === 'excellent' || multiModalTesting.compatibilityRating === 'good') {
      passedChecks = 1;
      evidence.push({
        type: 'text',
        description: 'Multi-modal interaction testing: Good compatibility rating',
        value: `Rating: ${multiModalTesting.compatibilityRating}, Score: ${(multiModalTesting.interactionScore * 100).toFixed(1)}%`,
        severity: 'info',
      });
    } else {
      issues.push(`Multi-modal interaction compatibility: ${multiModalTesting.compatibilityRating} rating`);
      evidence.push({
        type: 'code',
        description: `Multi-modal interaction testing: ${multiModalTesting.compatibilityRating} rating`,
        value: `Touch: ${multiModalTesting.supportsTouch}, Mouse: ${multiModalTesting.supportsMouse}, Keyboard: ${multiModalTesting.supportsKeyboard}, Pointer: ${multiModalTesting.supportsPointer}, Concurrent: ${multiModalTesting.concurrentSupport}`,
        severity: multiModalTesting.compatibilityRating === 'poor' ? 'error' : 'warning',
      });
      recommendations.push('Improve multi-modal interaction support for better accessibility');
    }

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Accessibility Input Assessment Algorithm
   */
  private async assessAccessibilityInputs(page: Page) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    const accessibilityAssessment = await page.$$eval(
      'input, button, select, textarea, [role="button"], [role="slider"], [role="spinbutton"]',
      (elements) => {
        return elements.map((element, index) => {
          const tagName = element.tagName.toLowerCase();
          const role = element.getAttribute('role');

          // Check for multiple input method support
          const hasKeyboardSupport = element.hasAttribute('tabindex') ||
                                   ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(element.tagName);
          const hasMouseSupport = element.hasAttribute('onclick') ||
                                element.hasAttribute('onmousedown');
          const hasTouchSupport = element.hasAttribute('ontouchstart') ||
                                element.hasAttribute('ontouchend');
          const hasPointerSupport = element.hasAttribute('onpointerdown') ||
                                  element.hasAttribute('onpointerup');

          const supportedMethods = [hasKeyboardSupport, hasMouseSupport, hasTouchSupport, hasPointerSupport]
            .filter(Boolean).length;

          // Check for accessibility features
          const hasAriaLabel = element.hasAttribute('aria-label') ||
                              element.hasAttribute('aria-labelledby');
          const hasDescription = element.hasAttribute('aria-describedby');
          const isAccessible = hasKeyboardSupport && (element.textContent || hasAriaLabel);

          return {
            index,
            tagName,
            role,
            selector: `${tagName}:nth-of-type(${index + 1})`,
            hasKeyboardSupport,
            hasMouseSupport,
            hasTouchSupport,
            hasPointerSupport,
            supportedMethods,
            hasAriaLabel,
            hasDescription,
            isAccessible,
            isMultiModal: supportedMethods >= 2,
          };
        });
      }
    );

    const totalChecks = accessibilityAssessment.length;
    let passedChecks = 0;

    accessibilityAssessment.forEach((element, index) => {
      if (element.isAccessible && element.isMultiModal) {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: `Input element ${index + 1} supports multiple input modalities`,
          value: `${element.selector} - ${element.supportedMethods} input methods supported`,
          severity: 'info',
        });
      } else {
        const missingFeatures = [];
        if (!element.isAccessible) missingFeatures.push('accessibility');
        if (!element.isMultiModal) missingFeatures.push('multi-modal support');

        issues.push(`Input element ${index + 1} lacks ${missingFeatures.join(' and ')}`);
        evidence.push({
          type: 'code',
          description: `Input element ${index + 1} needs improvement`,
          value: `${element.selector} - supported methods: ${element.supportedMethods}, accessible: ${element.isAccessible}`,
          severity: 'warning',
        });
        recommendations.push(`Improve ${missingFeatures.join(' and ')} for input element ${index + 1}`);
      }
    });

    return {
      totalChecks,
      passedChecks,
      evidence,
      issues,
      recommendations,
    };
  }

  // Analyze input mechanism restrictions
  const inputAnalysis = await page.evaluate(() => {
    const allElements = document.querySelectorAll('*');
      
      allElements.forEach((element, index) => {
        const computedStyle = window.getComputedStyle(element);
        const eventListeners: string[] = [];
        let hasRestrictions = false;
        let restrictionType = '';

        // Check for CSS properties that might restrict input
        const touchAction = computedStyle.touchAction;
        const pointerEvents = computedStyle.pointerEvents;
        const userSelect = computedStyle.userSelect;

        // Check for restrictive touch-action values
        if (touchAction && touchAction !== 'auto' && touchAction !== 'manipulation') {
          if (touchAction === 'none') {
            hasRestrictions = true;
            restrictionType = 'touch-disabled';
            eventListeners.push('touch-action: none');
          } else if (touchAction.includes('pan-') && !touchAction.includes('pinch-zoom')) {
            hasRestrictions = true;
            restrictionType = 'touch-restricted';
            eventListeners.push(`touch-action: ${touchAction}`);
          }
        }

        // Check for pointer-events restrictions
        if (pointerEvents === 'none') {
          hasRestrictions = true;
          restrictionType = 'pointer-disabled';
          eventListeners.push('pointer-events: none');
        }

        // Check for JavaScript that might detect and restrict input types
        const onclickHandler = element.getAttribute('onclick');
        if (onclickHandler) {
          const restrictivePatterns = [
            /touch.*only/i,
            /mouse.*only/i,
            /pointer.*only/i,
            /keyboard.*only/i,
            /preventDefault.*touch/i,
            /preventDefault.*mouse/i,
            /stopPropagation.*touch/i,
          ];

          restrictivePatterns.forEach(pattern => {
            if (pattern.test(onclickHandler)) {
              hasRestrictions = true;
              restrictionType = 'js-input-restriction';
              eventListeners.push('onclick-restriction');
            }
          });
        }

        // Check for data attributes that suggest input restrictions
        const restrictiveAttributes = [
          'data-touch-only',
          'data-mouse-only',
          'data-keyboard-only',
          'data-pointer-only',
          'data-no-touch',
          'data-no-mouse',
          'data-no-keyboard'
        ];

        restrictiveAttributes.forEach(attr => {
          if (element.hasAttribute(attr)) {
            hasRestrictions = true;
            restrictionType = 'attribute-restriction';
            eventListeners.push(attr);
          }
        });

        // Check for classes that suggest input restrictions
        const restrictiveClasses = [
          'touch-only',
          'mouse-only',
          'keyboard-only',
          'pointer-only',
          'no-touch',
          'no-mouse',
          'no-keyboard',
          'desktop-only',
          'mobile-only'
        ];

        restrictiveClasses.forEach(className => {
          if (element.classList.contains(className)) {
            hasRestrictions = true;
            restrictionType = 'class-restriction';
            eventListeners.push(`class:${className}`);
          }
        });

        if (hasRestrictions) {
          restrictiveElements.push({
            selector: generateSelector(element, index),
            tagName: element.tagName.toLowerCase(),
            restrictionType,
            hasPointerEvents: pointerEvents !== 'none',
            hasTouchEvents: touchAction !== 'none',
            hasKeyboardEvents: element.hasAttribute('tabindex') || ['button', 'a', 'input', 'textarea', 'select'].includes(element.tagName.toLowerCase()),
            hasMouseEvents: true, // Assume mouse events unless explicitly disabled
            eventListeners,
            attributes: getRelevantAttributes(element),
            cssProperties: {
              touchAction: touchAction || 'auto',
              pointerEvents: pointerEvents || 'auto',
              userSelect: userSelect || 'auto',
            },
          });
        }
      });

      return {
        restrictiveElements,
        totalElements: allElements.length,
      };

      function generateSelector(element: Element, index: number): string {
        if (element.id) return `#${element.id}`;
        if (element.className) {
          const classes = element.className.split(' ').filter(c => c.trim());
          if (classes.length > 0) return `.${classes[0]}`;
        }
        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      function getRelevantAttributes(element: Element): Record<string, string> {
        const attrs: Record<string, string> = {};
        const relevantAttrs = ['id', 'class', 'role', 'tabindex', 'onclick'];
        
        relevantAttrs.forEach(attr => {
          const value = element.getAttribute(attr);
          if (value) attrs[attr] = value;
        });

        return attrs;
      }
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const elementCount = inputAnalysis.restrictiveElements.length;

    if (elementCount > 0) {
      // Categorize restrictions by severity
      const severeRestrictions = inputAnalysis.restrictiveElements.filter(
        el => el.restrictionType === 'touch-disabled' || el.restrictionType === 'pointer-disabled'
      );
      const moderateRestrictions = inputAnalysis.restrictiveElements.filter(
        el => el.restrictionType === 'touch-restricted' || el.restrictionType === 'js-input-restriction'
      );
      const minorRestrictions = inputAnalysis.restrictiveElements.filter(
        el => el.restrictionType === 'attribute-restriction' || el.restrictionType === 'class-restriction'
      );

      // Calculate score penalties
      if (severeRestrictions.length > 0) {
        score -= Math.min(50, severeRestrictions.length * 20);
        issues.push(`${severeRestrictions.length} elements completely disable input mechanisms`);
      }

      if (moderateRestrictions.length > 0) {
        score -= Math.min(30, moderateRestrictions.length * 10);
        issues.push(`${moderateRestrictions.length} elements restrict specific input mechanisms`);
      }

      if (minorRestrictions.length > 0) {
        score -= Math.min(20, minorRestrictions.length * 5);
        issues.push(`${minorRestrictions.length} elements may have input mechanism preferences`);
      }

      evidence.push({
        type: 'interaction',
        description: 'Elements with input mechanism restrictions',
        value: `Found ${elementCount} elements that may restrict concurrent input mechanisms`,
        elementCount,
        affectedSelectors: inputAnalysis.restrictiveElements.map(el => el.selector),
        severity: severeRestrictions.length > 0 ? 'error' : 'warning',
        fixExample: {
          before: '.element { touch-action: none; pointer-events: none; }',
          after: '.element { touch-action: manipulation; /* Allow all input types */ }',
          description: 'Remove restrictions on input mechanisms to allow concurrent use',
          codeExample: `
/* Before: Restrictive input handling */
.touch-only {
  touch-action: none;
  pointer-events: none;
}

.mouse-only {
  /* JavaScript: if (!event.type.includes('mouse')) return; */
}

/* After: Inclusive input handling */
.inclusive-element {
  touch-action: manipulation; /* Allow touch and mouse */
  /* Support all input types simultaneously */
}

/* JavaScript: Handle all input types */
element.addEventListener('click', handleInteraction);
element.addEventListener('keydown', handleKeyboard);
element.addEventListener('touchstart', handleTouch);
// Don't prevent or restrict any input mechanism
          `,
          resources: [
            'https://www.w3.org/WAI/WCAG21/Understanding/concurrent-input-mechanisms.html',
            'https://developer.mozilla.org/en-US/docs/Web/CSS/touch-action',
            'https://developer.mozilla.org/en-US/docs/Web/API/Pointer_events'
          ]
        },
        metadata: {
          scanDuration,
          elementsAnalyzed: inputAnalysis.totalElements,
          checkSpecificData: {
            severeRestrictions: severeRestrictions.length,
            moderateRestrictions: moderateRestrictions.length,
            minorRestrictions: minorRestrictions.length,
            restrictionTypes: [...new Set(inputAnalysis.restrictiveElements.map(el => el.restrictionType))],
          },
        },
      });

      // Add specific examples for each type of restriction
      inputAnalysis.restrictiveElements.slice(0, 5).forEach(element => {
        const severityMap: Record<string, 'error' | 'warning' | 'info'> = {
          'touch-disabled': 'error',
          'pointer-disabled': 'error',
          'touch-restricted': 'warning',
          'js-input-restriction': 'warning',
          'attribute-restriction': 'info',
          'class-restriction': 'info',
        };

        evidence.push({
          type: 'code',
          description: `Input restriction: ${element.restrictionType}`,
          value: `<${element.tagName} ${Object.entries(element.attributes)
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ')}>`,
          selector: element.selector,
          severity: severityMap[element.restrictionType] || 'warning',
          metadata: {
            checkSpecificData: {
              restrictionType: element.restrictionType,
              eventListeners: element.eventListeners,
              cssProperties: element.cssProperties,
            },
          },
        });
      });

      recommendations.push('Remove restrictions that prevent concurrent use of input mechanisms');
      recommendations.push('Ensure touch, mouse, keyboard, and other input methods can be used simultaneously');
      recommendations.push('Avoid CSS properties like "touch-action: none" unless absolutely necessary');
      recommendations.push('Test with multiple input devices to ensure all work together');
      
      if (severeRestrictions.length > 0) {
        recommendations.push('CRITICAL: Some elements completely disable input mechanisms');
      }
    } else {
      // No input restrictions found
      evidence.push({
        type: 'info',
        description: 'No input mechanism restrictions detected',
        value: 'Page appears to support concurrent use of different input mechanisms',
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: inputAnalysis.totalElements,
          checkSpecificData: {
            noRestrictionsFound: true,
          },
        },
      });
    }

    // Ensure score doesn't go below 0
    score = Math.max(0, score);

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }
}
